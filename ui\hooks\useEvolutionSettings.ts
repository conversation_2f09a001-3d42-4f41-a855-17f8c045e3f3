import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface EvolutionSettings {
  evolution_enabled: boolean;
  auto_optimization: boolean;
  system_status: {
    memory_engine: string;
    vector_store: string;
    evolution_service: string;
    prompt_system: string;
  };
}

interface UseEvolutionSettingsReturn {
  settings: EvolutionSettings | null;
  isLoading: boolean;
  error: string | null;
  fetchSettings: () => Promise<void>;
  updateSettings: (newSettings: Partial<EvolutionSettings>) => Promise<void>;
  runMemoryAnalysis: () => Promise<void>;
  generatePerformanceReport: () => Promise<void>;
}

export function useEvolutionSettings(): UseEvolutionSettingsReturn {
  const [settings, setSettings] = useState<EvolutionSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Use the same API URL pattern as other hooks
  const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";

  const fetchSettings = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/api/v1/evolution-config/settings`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch settings: ${response.statusText}`);
      }

      const data = await response.json();
      setSettings(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch evolution settings';
      setError(errorMessage);
      console.error('Error fetching evolution settings:', err);
    } finally {
      setIsLoading(false);
    }
  }, [API_URL]);

  const updateSettings = useCallback(async (newSettings: Partial<EvolutionSettings>) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/api/v1/evolution-config/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });

      if (!response.ok) {
        throw new Error(`Failed to update settings: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Update local state with the new settings
        setSettings(prev => prev ? { ...prev, ...newSettings } : null);
        toast({
          title: "Settings Updated",
          description: data.message || "Evolution settings updated successfully",
        });
      } else {
        throw new Error(data.message || 'Failed to update settings');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update evolution settings';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [API_URL, toast]);

  const runMemoryAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // For now, simulate the analysis
      toast({
        title: "Memory Analysis",
        description: "Starting comprehensive memory analysis...",
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, this would call:
      // const response = await fetch('/api/v1/evolution-config/analysis', { method: 'POST' });
      
      toast({
        title: "Analysis Complete",
        description: "Memory analysis completed successfully. Check the reports section for details.",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to run memory analysis';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const generatePerformanceReport = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      toast({
        title: "Performance Report",
        description: "Generating performance report...",
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));

      // In a real implementation, this would call:
      // const response = await fetch('/api/v1/evolution-config/performance-report', { method: 'POST' });
      // const blob = await response.blob();
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = 'performance-report.pdf';
      // a.click();

      // For now, simulate download
      const reportData = {
        timestamp: new Date().toISOString(),
        metrics: {
          learning_efficiency: "78%",
          memory_quality: "92%",
          operation_success: "85%",
          system_uptime: "99.7%"
        },
        recommendations: [
          "Consider increasing memory consolidation frequency",
          "Review custom prompt configurations for optimization",
          "Monitor vector store performance during peak hours"
        ]
      };

      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Report Generated",
        description: "Performance report generated and downloaded successfully.",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate performance report';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  return {
    settings,
    isLoading,
    error,
    fetchSettings,
    updateSettings,
    runMemoryAnalysis,
    generatePerformanceReport,
  };
}
