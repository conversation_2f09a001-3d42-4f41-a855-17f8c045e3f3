"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import Editor from "@monaco-editor/react";
import {
  Code,
  Save,
  RotateCcw,
  Download,
  Upload,
  History,
  Eye,
  AlertCircle,
  CheckCircle,
  FileText,
  Clock,
  BookOpen,
  Split,
  Maximize2
} from "lucide-react";
import { TemplateLibraryModal } from "./TemplateLibraryModal";
import { VersionHistoryModal } from "./VersionHistoryModal";

interface PromptConfig {
  id: string;
  name: string;
  content: string;
  lastModified: string;
  version: string;
  isDefault: boolean;
  characterCount: number;
  validationErrors: string[];
  autoSaveEnabled: boolean;
}

interface PromptVersion {
  id: string;
  version: string;
  content: string;
  timestamp: string;
  author: string;
  changes: string;
}

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  category: "fact-extraction" | "memory-evolution" | "general";
  tags: string[];
}

interface PromptsTabProps {
  onSettingsChange: (hasChanges: boolean) => void;
}

export function PromptsTab({ onSettingsChange }: PromptsTabProps) {
  const autoSaveIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [sideBySideView, setSideBySideView] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<PromptTemplate | null>(null);

  const [factExtractionPrompt, setFactExtractionPrompt] = useState<PromptConfig>({
    id: "fact-extraction",
    name: "Fact Extraction Prompt",
    content: `You are a fact extraction system. Your task is to analyze the given text and extract meaningful facts that should be stored in memory.

Guidelines:
1. Extract only factual information, not opinions or speculation
2. Focus on entities, relationships, and concrete details
3. Preserve context and temporal information when available
4. Format facts as clear, concise statements
5. Avoid redundancy with existing knowledge

Input: {input_text}
Context: {existing_context}

Extract facts in the following format:
- Fact: [Clear factual statement]
- Confidence: [High/Medium/Low]
- Category: [Entity/Relationship/Event/Attribute]
- Source: [Reference to input section]`,
    lastModified: "2024-01-15T10:30:00Z",
    version: "1.2.0",
    isDefault: false,
    characterCount: 0,
    validationErrors: [],
    autoSaveEnabled: true
  });

  const [memoryEvolutionPrompt, setMemoryEvolutionPrompt] = useState<PromptConfig>({
    id: "memory-evolution",
    name: "Memory Evolution Prompt",
    content: `You are a memory evolution system. Your task is to determine how new information should update existing memories.

Guidelines:
1. Analyze conflicts between new and existing information
2. Determine the most appropriate action: ADD, UPDATE, DELETE, or NOOP
3. Consider recency, reliability, and context
4. Preserve important historical information
5. Merge complementary information when possible

Existing Memory: {existing_memory}
New Information: {new_information}
Context: {context}

Provide your decision in this format:
- Action: [ADD/UPDATE/DELETE/NOOP]
- Confidence: [High/Medium/Low]
- Reasoning: [Brief explanation]
- Updated Content: [New memory content if applicable]`,
    lastModified: "2024-01-10T14:20:00Z",
    version: "1.1.0",
    isDefault: true,
    characterCount: 0,
    validationErrors: [],
    autoSaveEnabled: true
  });

  const [promptVersions, setPromptVersions] = useState<PromptVersion[]>([
    {
      id: "1",
      version: "1.2.0",
      content: factExtractionPrompt.content,
      timestamp: "2024-01-15T10:30:00Z",
      author: "System",
      changes: "Updated fact extraction guidelines"
    },
    {
      id: "2",
      version: "1.1.0",
      content: memoryEvolutionPrompt.content,
      timestamp: "2024-01-10T14:20:00Z",
      author: "System",
      changes: "Initial memory evolution prompt"
    }
  ]);

  const [promptTemplates, setPromptTemplates] = useState<PromptTemplate[]>([
    {
      id: "1",
      name: "Technical Documentation Extractor",
      description: "Optimized for extracting facts from technical documentation",
      content: `Extract technical facts from documentation...`,
      category: "fact-extraction",
      tags: ["technical", "documentation", "api"]
    },
    {
      id: "2",
      name: "Business Process Analyzer",
      description: "Focused on business process and decision extraction",
      content: `Analyze business processes and decisions...`,
      category: "fact-extraction",
      tags: ["business", "process", "decision"]
    },
    {
      id: "3",
      name: "Conservative Memory Evolution",
      description: "Cautious approach to memory updates",
      content: `Conservative memory evolution approach...`,
      category: "memory-evolution",
      tags: ["conservative", "safe", "minimal-changes"]
    }
  ]);

  const [activePrompt, setActivePrompt] = useState<"fact-extraction" | "memory-evolution">("fact-extraction");
  const [showPreview, setShowPreview] = useState(false);
  const [lastAutoSave, setLastAutoSave] = useState<Date | null>(null);

  // Auto-save functionality
  useEffect(() => {
    const currentPrompt = activePrompt === "fact-extraction" ? factExtractionPrompt : memoryEvolutionPrompt;

    if (currentPrompt.autoSaveEnabled) {
      if (autoSaveIntervalRef.current) {
        clearTimeout(autoSaveIntervalRef.current);
      }

      autoSaveIntervalRef.current = setTimeout(() => {
        handleAutoSave(activePrompt);
      }, 30000); // 30 seconds
    }

    return () => {
      if (autoSaveIntervalRef.current) {
        clearTimeout(autoSaveIntervalRef.current);
      }
    };
  }, [factExtractionPrompt.content, memoryEvolutionPrompt.content, activePrompt]);

  const handleAutoSave = async (promptId: string) => {
    try {
      // Auto-save logic would go here
      console.log(`Auto-saving ${promptId} prompt`);
      setLastAutoSave(new Date());
    } catch (error) {
      console.error("Auto-save failed:", error);
    }
  };

  // Validation function
  const validatePrompt = (content: string, type: "fact-extraction" | "memory-evolution"): string[] => {
    const errors: string[] = [];

    if (content.length === 0) {
      errors.push("Prompt content cannot be empty");
    }

    if (content.length > 4000) {
      errors.push("Prompt content exceeds 4000 character limit");
    }

    // Check for required sections based on prompt type
    if (type === "fact-extraction") {
      if (!content.includes("{input_text}")) {
        errors.push("Fact extraction prompt must include {input_text} placeholder");
      }
      if (!content.toLowerCase().includes("guidelines")) {
        errors.push("Fact extraction prompt should include guidelines section");
      }
    } else {
      if (!content.includes("{existing_memory}") || !content.includes("{new_information}")) {
        errors.push("Memory evolution prompt must include {existing_memory} and {new_information} placeholders");
      }
      if (!content.toLowerCase().includes("action")) {
        errors.push("Memory evolution prompt should specify action format");
      }
    }

    return errors;
  };

  // Update character counts
  useEffect(() => {
    setFactExtractionPrompt(prev => ({
      ...prev,
      characterCount: prev.content.length
    }));
  }, [factExtractionPrompt.content]);

  useEffect(() => {
    setMemoryEvolutionPrompt(prev => ({
      ...prev,
      characterCount: prev.content.length
    }));
  }, [memoryEvolutionPrompt.content]);

  const handlePromptChange = (promptId: string, content: string) => {
    const validationErrors = validatePrompt(content, promptId as "fact-extraction" | "memory-evolution");

    if (promptId === "fact-extraction") {
      setFactExtractionPrompt(prev => ({
        ...prev,
        content,
        lastModified: new Date().toISOString(),
        validationErrors
      }));
    } else {
      setMemoryEvolutionPrompt(prev => ({
        ...prev,
        content,
        lastModified: new Date().toISOString(),
        validationErrors
      }));
    }
    onSettingsChange(true);
  };

  const handleTemplateApply = (template: PromptTemplate) => {
    const targetPromptId = template.category === "fact-extraction" ? "fact-extraction" : "memory-evolution";
    handlePromptChange(targetPromptId, template.content);
    setShowTemplateLibrary(false);
  };

  const toggleAutoSave = (promptId: string, enabled: boolean) => {
    if (promptId === "fact-extraction") {
      setFactExtractionPrompt(prev => ({ ...prev, autoSaveEnabled: enabled }));
    } else {
      setMemoryEvolutionPrompt(prev => ({ ...prev, autoSaveEnabled: enabled }));
    }
    onSettingsChange(true);
  };

  const handleSavePrompt = (promptId: string) => {
    // Implementation for saving prompt
    console.log(`Saving prompt: ${promptId}`);
  };

  const handleResetPrompt = (promptId: string) => {
    if (confirm("Are you sure you want to reset this prompt to default? This action cannot be undone.")) {
      // Implementation for resetting to default
      console.log(`Resetting prompt: ${promptId}`);
      onSettingsChange(true);
    }
  };

  const handleExportPrompt = (prompt: PromptConfig) => {
    const dataStr = JSON.stringify(prompt, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${prompt.id}-prompt-${prompt.version}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleImportPrompt = (promptId: string) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const imported = JSON.parse(e.target?.result as string);
            handlePromptChange(promptId, imported.content);
          } catch (error) {
            alert('Invalid prompt file format');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const getCurrentPrompt = () => {
    return activePrompt === "fact-extraction" ? factExtractionPrompt : memoryEvolutionPrompt;
  };

  const getCharacterLimitColor = (count: number) => {
    if (count > 3800) return "text-red-400";
    if (count > 3500) return "text-yellow-400";
    return "text-zinc-400";
  };

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Custom Prompts</h3>
          <p className="text-sm text-zinc-400">
            Configure prompts for fact extraction and memory evolution processes
          </p>
        </div>

        <div className="flex items-center gap-3">
          {lastAutoSave && (
            <div className="flex items-center gap-1 text-xs text-zinc-400">
              <Clock className="h-3 w-3" />
              Last saved: {lastAutoSave.toLocaleTimeString()}
            </div>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTemplateLibrary(true)}
            className="border-zinc-700"
          >
            <BookOpen className="mr-2 h-4 w-4" />
            Templates
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowVersionHistory(true)}
            className="border-zinc-700"
          >
            <History className="mr-2 h-4 w-4" />
            History
          </Button>

          <div className="flex items-center gap-2">
            <Label htmlFor="side-by-side" className="text-sm">Side-by-side</Label>
            <Switch
              id="side-by-side"
              checked={sideBySideView}
              onCheckedChange={setSideBySideView}
            />
          </div>
        </div>
      </div>

      {/* Editor Layout */}
      {sideBySideView ? (
        /* Side-by-side Editor */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Fact Extraction Editor */}
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {factExtractionPrompt.name}
                  </CardTitle>
                  <CardDescription>
                    Prompt used to extract facts from input text
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={factExtractionPrompt.isDefault ? "secondary" : "default"}>
                    {factExtractionPrompt.isDefault ? "Default" : "Custom"}
                  </Badge>
                  <Badge variant="outline">
                    v{factExtractionPrompt.version}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Character Count and Auto-save */}
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center gap-4">
                  <span className={getCharacterLimitColor(factExtractionPrompt.characterCount)}>
                    {factExtractionPrompt.characterCount} / 4000
                  </span>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={factExtractionPrompt.autoSaveEnabled}
                      onCheckedChange={(checked) => toggleAutoSave("fact-extraction", checked)}
                      size="sm"
                    />
                    <span className="text-zinc-400">Auto-save</span>
                  </div>
                </div>
              </div>

              {/* Validation Errors */}
              {factExtractionPrompt.validationErrors.length > 0 && (
                <div className="space-y-1">
                  {factExtractionPrompt.validationErrors.map((error, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-red-400">
                      <AlertCircle className="h-3 w-3" />
                      {error}
                    </div>
                  ))}
                </div>
              )}

              {/* Monaco Editor */}
              <div className="border border-zinc-700 rounded-lg overflow-hidden">
                <Editor
                  height="400px"
                  defaultLanguage="markdown"
                  value={factExtractionPrompt.content}
                  onChange={(value) => handlePromptChange("fact-extraction", value || "")}
                  theme="vs-dark"
                  options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: "on",
                    wordWrap: "on",
                    automaticLayout: true
                  }}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => handleSavePrompt("fact-extraction")}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleResetPrompt("fact-extraction")}
                  className="border-zinc-700"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportPrompt(factExtractionPrompt)}
                  className="border-zinc-700"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Memory Evolution Editor */}
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    {memoryEvolutionPrompt.name}
                  </CardTitle>
                  <CardDescription>
                    Prompt used to determine memory evolution operations
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={memoryEvolutionPrompt.isDefault ? "secondary" : "default"}>
                    {memoryEvolutionPrompt.isDefault ? "Default" : "Custom"}
                  </Badge>
                  <Badge variant="outline">
                    v{memoryEvolutionPrompt.version}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Character Count and Auto-save */}
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center gap-4">
                  <span className={getCharacterLimitColor(memoryEvolutionPrompt.characterCount)}>
                    {memoryEvolutionPrompt.characterCount} / 4000
                  </span>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={memoryEvolutionPrompt.autoSaveEnabled}
                      onCheckedChange={(checked) => toggleAutoSave("memory-evolution", checked)}
                      size="sm"
                    />
                    <span className="text-zinc-400">Auto-save</span>
                  </div>
                </div>
              </div>

              {/* Validation Errors */}
              {memoryEvolutionPrompt.validationErrors.length > 0 && (
                <div className="space-y-1">
                  {memoryEvolutionPrompt.validationErrors.map((error, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-red-400">
                      <AlertCircle className="h-3 w-3" />
                      {error}
                    </div>
                  ))}
                </div>
              )}

              {/* Monaco Editor */}
              <div className="border border-zinc-700 rounded-lg overflow-hidden">
                <Editor
                  height="400px"
                  defaultLanguage="markdown"
                  value={memoryEvolutionPrompt.content}
                  onChange={(value) => handlePromptChange("memory-evolution", value || "")}
                  theme="vs-dark"
                  options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    lineNumbers: "on",
                    wordWrap: "on",
                    automaticLayout: true
                  }}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => handleSavePrompt("memory-evolution")}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleResetPrompt("memory-evolution")}
                  className="border-zinc-700"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportPrompt(memoryEvolutionPrompt)}
                  className="border-zinc-700"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Tabbed Editor */
        <Tabs value={activePrompt} onValueChange={(value) => setActivePrompt(value as any)}>
          <TabsList className="grid w-full grid-cols-2 bg-zinc-900/50 border border-zinc-800">
            <TabsTrigger value="fact-extraction" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Fact Extraction
            </TabsTrigger>
            <TabsTrigger value="memory-evolution" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Memory Evolution
            </TabsTrigger>
          </TabsList>

        <TabsContent value="fact-extraction" className="space-y-4">
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {factExtractionPrompt.name}
                  </CardTitle>
                  <CardDescription>
                    Prompt used to extract facts from input text
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={factExtractionPrompt.isDefault ? "secondary" : "default"}>
                    {factExtractionPrompt.isDefault ? "Default" : "Custom"}
                  </Badge>
                  <Badge variant="outline">
                    v{factExtractionPrompt.version}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Character Count and Auto-save */}
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center gap-4">
                  <span className={getCharacterLimitColor(factExtractionPrompt.characterCount)}>
                    {factExtractionPrompt.characterCount} / 4000
                  </span>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={factExtractionPrompt.autoSaveEnabled}
                      onCheckedChange={(checked) => toggleAutoSave("fact-extraction", checked)}
                      size="sm"
                    />
                    <span className="text-zinc-400">Auto-save</span>
                  </div>
                </div>
              </div>

              {/* Validation Errors */}
              {factExtractionPrompt.validationErrors.length > 0 && (
                <div className="space-y-1">
                  {factExtractionPrompt.validationErrors.map((error, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-red-400">
                      <AlertCircle className="h-3 w-3" />
                      {error}
                    </div>
                  ))}
                </div>
              )}

              {/* Monaco Editor */}
              <div className="space-y-2">
                <Label htmlFor="fact-extraction-prompt">Prompt Content</Label>
                <div className="border border-zinc-700 rounded-lg overflow-hidden">
                  <Editor
                    height="400px"
                    defaultLanguage="markdown"
                    value={factExtractionPrompt.content}
                    onChange={(value) => handlePromptChange("fact-extraction", value || "")}
                    theme="vs-dark"
                    options={{
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      fontSize: 14,
                      lineNumbers: "on",
                      wordWrap: "on",
                      automaticLayout: true
                    }}
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => handleSavePrompt("fact-extraction")}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleResetPrompt("fact-extraction")}
                  className="border-zinc-700"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset to Default
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportPrompt(factExtractionPrompt)}
                  className="border-zinc-700"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleImportPrompt("fact-extraction")}
                  className="border-zinc-700"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="memory-evolution" className="space-y-4">
          <Card className="bg-zinc-900/50 border-zinc-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    {memoryEvolutionPrompt.name}
                  </CardTitle>
                  <CardDescription>
                    Prompt used to determine memory evolution operations
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={memoryEvolutionPrompt.isDefault ? "secondary" : "default"}>
                    {memoryEvolutionPrompt.isDefault ? "Default" : "Custom"}
                  </Badge>
                  <Badge variant="outline">
                    v{memoryEvolutionPrompt.version}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Character Count and Auto-save */}
              <div className="flex justify-between items-center text-sm">
                <div className="flex items-center gap-4">
                  <span className={getCharacterLimitColor(memoryEvolutionPrompt.characterCount)}>
                    {memoryEvolutionPrompt.characterCount} / 4000
                  </span>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={memoryEvolutionPrompt.autoSaveEnabled}
                      onCheckedChange={(checked) => toggleAutoSave("memory-evolution", checked)}
                      size="sm"
                    />
                    <span className="text-zinc-400">Auto-save</span>
                  </div>
                </div>
              </div>

              {/* Validation Errors */}
              {memoryEvolutionPrompt.validationErrors.length > 0 && (
                <div className="space-y-1">
                  {memoryEvolutionPrompt.validationErrors.map((error, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-red-400">
                      <AlertCircle className="h-3 w-3" />
                      {error}
                    </div>
                  ))}
                </div>
              )}

              {/* Monaco Editor */}
              <div className="space-y-2">
                <Label htmlFor="memory-evolution-prompt">Prompt Content</Label>
                <div className="border border-zinc-700 rounded-lg overflow-hidden">
                  <Editor
                    height="400px"
                    defaultLanguage="markdown"
                    value={memoryEvolutionPrompt.content}
                    onChange={(value) => handlePromptChange("memory-evolution", value || "")}
                    theme="vs-dark"
                    options={{
                      minimap: { enabled: false },
                      scrollBeyondLastLine: false,
                      fontSize: 14,
                      lineNumbers: "on",
                      wordWrap: "on",
                      automaticLayout: true
                    }}
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => handleSavePrompt("memory-evolution")}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleResetPrompt("memory-evolution")}
                  className="border-zinc-700"
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset to Default
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportPrompt(memoryEvolutionPrompt)}
                  className="border-zinc-700"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleImportPrompt("memory-evolution")}
                  className="border-zinc-700"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>
      )}

      {/* Prompt Validation Status */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Validation Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 rounded-lg bg-zinc-800/50">
              <span className="text-sm">Fact Extraction Prompt</span>
              <Badge className={factExtractionPrompt.validationErrors.length === 0
                ? "bg-green-900/20 text-green-400 border-green-800"
                : "bg-red-900/20 text-red-400 border-red-800"}>
                {factExtractionPrompt.validationErrors.length === 0 ? (
                  <>
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Valid
                  </>
                ) : (
                  <>
                    <AlertCircle className="mr-1 h-3 w-3" />
                    {factExtractionPrompt.validationErrors.length} Error{factExtractionPrompt.validationErrors.length > 1 ? 's' : ''}
                  </>
                )}
              </Badge>
            </div>
            <div className="flex items-center justify-between p-3 rounded-lg bg-zinc-800/50">
              <span className="text-sm">Memory Evolution Prompt</span>
              <Badge className={memoryEvolutionPrompt.validationErrors.length === 0
                ? "bg-green-900/20 text-green-400 border-green-800"
                : "bg-red-900/20 text-red-400 border-red-800"}>
                {memoryEvolutionPrompt.validationErrors.length === 0 ? (
                  <>
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Valid
                  </>
                ) : (
                  <>
                    <AlertCircle className="mr-1 h-3 w-3" />
                    {memoryEvolutionPrompt.validationErrors.length} Error{memoryEvolutionPrompt.validationErrors.length > 1 ? 's' : ''}
                  </>
                )}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Template Library Modal */}
      <TemplateLibraryModal
        isOpen={showTemplateLibrary}
        onClose={() => setShowTemplateLibrary(false)}
        templates={promptTemplates}
        onApplyTemplate={handleTemplateApply}
      />

      {/* Version History Modal */}
      <VersionHistoryModal
        isOpen={showVersionHistory}
        onClose={() => setShowVersionHistory(false)}
        versions={promptVersions}
        onRestoreVersion={(version) => {
          // Determine which prompt to restore based on version content
          const isFactExtraction = version.content.includes("{input_text}");
          const promptId = isFactExtraction ? "fact-extraction" : "memory-evolution";
          handlePromptChange(promptId, version.content);
          setShowVersionHistory(false);
        }}
      />
    </div>
  );
}
