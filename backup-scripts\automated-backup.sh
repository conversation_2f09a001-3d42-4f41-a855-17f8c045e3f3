#!/bin/bash
# automated-backup.sh - Automated MinIO backup with logging

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_DIR/logs/backup.log"
TELEGRAM_WEBHOOK_URL="https://workflow.syncrobit.net/webhook/v1/send-telegram-notification"

send_telegram_notification() {
    local message=$1
    curl -s -X POST -H "Content-Type: text/plain" -d "$message" $TELEGRAM_WEBHOOK_URL
}

# Change to project directory
cd "$PROJECT_DIR"

# Log start
echo "$(date): Starting automated Memory-Master-v2 backup" >> $LOG_FILE

# Execute backup
if $SCRIPT_DIR/host-backup-to-minio.sh >> $LOG_FILE 2>&1; then
    echo "$(date): ✅ Backup completed successfully" >> $LOG_FILE
    send_telegram_notification "✅ Memory-Master backup completed successfully."
else
    echo "$(date): ❌ Backup failed" >> $LOG_FILE
    send_telegram_notification "❌ Memory-Master backup FAILED."
fi