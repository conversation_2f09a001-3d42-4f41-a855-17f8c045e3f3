import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Check if authentication is enabled
  const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'

  // If auth is disabled, return with security headers only
  if (!isAuthEnabled) {
    const response = NextResponse.next()
    addSecurityHeaders(response)
    return response
  }

  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/auth/login',
    '/auth/signup',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/_next',
    '/favicon.ico',
    '/logo.svg',
    '/api/health',
    '/images'
  ]

  // Check if current path is public
  const isPublicRoute = publicRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.
  const {
    data: { user },
  } = await supabase.auth.getUser()

  // If trying to access auth pages and we have a user, redirect to dashboard
  if (user && request.nextUrl.pathname.startsWith('/auth/')) {
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/'
    addSecurityHeaders(supabaseResponse)
    return NextResponse.redirect(redirectUrl)
  }

  // If trying to access protected routes without authentication, redirect to login
  if (!user && !isPublicRoute) {
    const redirectUrl = request.nextUrl.clone()
    redirectUrl.pathname = '/auth/login'
    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)
    addSecurityHeaders(supabaseResponse)
    return NextResponse.redirect(redirectUrl)
  }

  // Session timeout check (30 minutes idle)
  if (user && !isPublicRoute) {
    const sessionCookie = request.cookies.get('session-timestamp')
    const sessionTimestamp = sessionCookie ? parseInt(sessionCookie.value) : 0
    const now = Date.now()
    const thirtyMinutes = 30 * 60 * 1000

    if (sessionTimestamp && (now - sessionTimestamp) > thirtyMinutes) {
      // Session expired due to inactivity
      const redirectUrl = request.nextUrl.clone()
      redirectUrl.pathname = '/auth/login'
      redirectUrl.searchParams.set('reason', 'session_timeout')

      const response = NextResponse.redirect(redirectUrl)
      // Clear session cookies
      response.cookies.delete('session-timestamp')
      addSecurityHeaders(response)
      return response
    }

    // Update session timestamp
    supabaseResponse.cookies.set('session-timestamp', now.toString(), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 8 * 60 * 60 // 8 hours max session
    })
  }

  // Add security headers and return the supabase response
  addSecurityHeaders(supabaseResponse)
  return supabaseResponse
}

function addSecurityHeaders(response: NextResponse) {
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')

  // Add Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://*.supabase.co http://localhost:8765 http://*************:8000",
    "frame-ancestors 'none'"
  ].join('; ')
  response.headers.set('Content-Security-Policy', csp)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - logo.svg (logo file)
     */
    '/((?!_next/static|_next/image|favicon.ico|logo.svg|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}