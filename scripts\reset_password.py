#!/usr/bin/env python3
"""
Password Reset Script for Memory Master

This script provides utilities for password management in Supabase Auth.

Usage:
    python scripts/reset_password.py --email <EMAIL>
    python scripts/reset_password.py --list-users
    python scripts/reset_password.py --generate-reset-link <EMAIL>

Environment Variables Required:
    SUPABASE_URL - Your Supabase project URL
    SUPABASE_SERVICE_ROLE_KEY - Your Supabase service role key
"""

import os
import sys
import argparse
import logging
import secrets
import string
from typing import Optional

try:
    from supabase import create_client, Client
    from supabase.client import AuthAPIError
except ImportError:
    print("Error: Supabase library not installed. Run: pip install supabase")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PasswordManager:
    def __init__(self):
        """Initialize the PasswordManager with Supabase client."""
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not self.supabase_url or not self.service_key:
            raise ValueError(
                "Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY"
            )
        
        self.supabase: Client = create_client(self.supabase_url, self.service_key)
        logger.info("✅ Supabase client initialized successfully")

    def generate_secure_password(self, length: int = 16) -> str:
        """Generate a secure random password."""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password

    def find_user_by_email(self, email: str) -> Optional[dict]:
        """Find a user by email address."""
        try:
            users = self.supabase.auth.admin.list_users()
            for user in users.users:
                if user.email == email:
                    return user
            return None
        except Exception as e:
            logger.error(f"Error finding user {email}: {e}")
            return None

    def reset_user_password(self, email: str, new_password: Optional[str] = None) -> dict:
        """Reset a user's password."""
        try:
            # Find the user first
            user = self.find_user_by_email(email)
            if not user:
                return {'success': False, 'error': f'User {email} not found'}
            
            # Generate password if not provided
            if not new_password:
                new_password = self.generate_secure_password()
            
            # Update user password
            logger.info(f"Resetting password for user: {email}")
            result = self.supabase.auth.admin.update_user_by_id(
                user.id,
                {'password': new_password}
            )
            
            if result.user:
                logger.info(f"✅ Password reset successfully for: {email}")
                return {
                    'success': True,
                    'email': email,
                    'user_id': user.id,
                    'new_password': new_password
                }
            else:
                return {'success': False, 'error': 'Failed to update password'}
                
        except AuthAPIError as e:
            logger.error(f"❌ Auth API error resetting password for {email}: {e}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            logger.error(f"❌ Unexpected error resetting password for {email}: {e}")
            return {'success': False, 'error': str(e)}

    def generate_password_reset_link(self, email: str) -> dict:
        """Generate a password reset link for a user."""
        try:
            # Find the user first
            user = self.find_user_by_email(email)
            if not user:
                return {'success': False, 'error': f'User {email} not found'}
            
            # Note: Supabase doesn't provide a direct API to generate password reset links
            # This would typically be done through the Supabase dashboard or by sending
            # a password reset email
            logger.info(f"Password reset process for user: {email}")
            
            return {
                'success': True,
                'email': email,
                'message': 'Password reset should be initiated through Supabase dashboard or by calling auth.resetPasswordForEmail()',
                'instructions': [
                    "1. Go to Supabase Dashboard > Authentication > Users",
                    f"2. Find user {email}",
                    "3. Click 'Send password reset email'",
                    "OR use the client SDK: supabase.auth.resetPasswordForEmail()"
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating reset link for {email}: {e}")
            return {'success': False, 'error': str(e)}

    def list_all_users(self) -> dict:
        """List all users in the system."""
        try:
            logger.info("Fetching all users...")
            result = self.supabase.auth.admin.list_users()
            
            users_info = []
            for user in result.users:
                users_info.append({
                    'id': user.id,
                    'email': user.email,
                    'created_at': user.created_at,
                    'last_sign_in_at': user.last_sign_in_at,
                    'email_confirmed_at': user.email_confirmed_at
                })
            
            return {
                'success': True,
                'count': len(users_info),
                'users': users_info
            }
            
        except Exception as e:
            logger.error(f"Error listing users: {e}")
            return {'success': False, 'error': str(e)}

    def print_users_table(self, users_data: dict):
        """Print users in a formatted table."""
        if not users_data['success']:
            print(f"❌ Error: {users_data['error']}")
            return
        
        users = users_data['users']
        if not users:
            print("No users found.")
            return
        
        print(f"\n📋 Found {users_data['count']} users:")
        print("-" * 100)
        print(f"{'Email':<35} {'User ID':<40} {'Created':<20}")
        print("-" * 100)
        
        for user in users:
            created = user['created_at'][:16] if user['created_at'] else 'N/A'
            print(f"{user['email']:<35} {user['id']:<40} {created:<20}")
        
        print("-" * 100)

def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description='Memory Master Password Management')
    parser.add_argument('--email', help='Email address of the user')
    parser.add_argument('--password', help='New password (if not provided, will generate one)')
    parser.add_argument('--list-users', action='store_true', help='List all users')
    parser.add_argument('--generate-reset-link', help='Generate password reset instructions for email')
    
    args = parser.parse_args()
    
    if not any([args.email, args.list_users, args.generate_reset_link]):
        parser.print_help()
        print("\nExamples:")
        print("  python scripts/reset_password.py --list-users")
        print("  python scripts/reset_password.py --email <EMAIL>")
        print("  python scripts/reset_password.py --email <EMAIL> --password newpassword123")
        print("  python scripts/reset_password.py --generate-reset-link <EMAIL>")
        sys.exit(1)
    
    try:
        password_manager = PasswordManager()
        
        if args.list_users:
            users_data = password_manager.list_all_users()
            password_manager.print_users_table(users_data)
        
        elif args.email:
            result = password_manager.reset_user_password(args.email, args.password)
            
            if result['success']:
                print(f"\n🎉 Password reset successful!")
                print(f"Email: {result['email']}")
                print(f"User ID: {result['user_id']}")
                print(f"New Password: {result['new_password']}")
                print("\n🔐 SECURITY REMINDER:")
                print("- Share this password securely with the user")
                print("- User should change password after first login")
                print("- Consider enabling MFA for additional security")
            else:
                print(f"❌ Password reset failed: {result['error']}")
                sys.exit(1)
        
        elif args.generate_reset_link:
            result = password_manager.generate_password_reset_link(args.generate_reset_link)
            
            if result['success']:
                print(f"\n📧 Password reset instructions for: {result['email']}")
                print(result['message'])
                print("\nInstructions:")
                for instruction in result['instructions']:
                    print(f"  {instruction}")
            else:
                print(f"❌ Failed to generate reset instructions: {result['error']}")
                sys.exit(1)
                
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("\n💡 Make sure to set the required environment variables:")
        print("   export SUPABASE_URL='your-supabase-url'")
        print("   export SUPABASE_SERVICE_ROLE_KEY='your-service-role-key'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()