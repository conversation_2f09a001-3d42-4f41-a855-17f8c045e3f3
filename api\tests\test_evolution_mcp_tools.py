"""Test suite for evolution intelligence MCP tools integration.

This module tests the MCP server tools for evolution intelligence,
including tool functionality, parameter validation, and integration with the core system.
"""

import pytest
import uuid
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import sessionmaker

from app.database import Base
from app.models import User, App, Memory, EvolutionOperation, EvolutionInsight, EvolutionOperationType
from app.services.evolution_service import EvolutionService


class TestEvolutionMCPTools:
    """Test evolution intelligence MCP tools."""
    
    @pytest.fixture(autouse=True)
    def setup_mcp_test_environment(self, test_db_session):
        """Set up MCP test environment."""
        self.session = test_db_session
        
        # Create test entities
        self.test_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            name="MCP Test User"
        )
        self.session.add(self.test_user)
        
        self.test_app = App(
            id=uuid.uuid4(),
            name="MCP Test App",
            owner_id=self.test_user.id
        )
        self.session.add(self.test_app)
        
        self.test_memory = Memory(
            id=uuid.uuid4(),
            user_id=self.test_user.id,
            app_id=self.test_app.id,
            content="Test memory content for MCP",
            metadata={"test": "mcp_integration"}
        )
        self.session.add(self.test_memory)
        
        self.session.commit()
        
        # Mock evolution service
        self.evolution_service = Mock(spec=EvolutionService)
    
    def test_mcp_tool_get_evolution_metrics(self):
        """Test MCP tool for getting evolution metrics."""
        # Create test data
        base_date = datetime.now().date() - timedelta(days=7)
        
        # Create operations for the last week
        operations = []
        for day in range(7):
            for op_type in EvolutionOperationType:
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=self.test_user.id,
                    app_id=self.test_app.id,
                    operation_type=op_type,
                    candidate_fact=f"MCP test fact {day}-{op_type.value}",
                    confidence_score=0.7 + (day * 0.05),
                    reasoning=f"MCP test reasoning {day}-{op_type.value}",
                    similarity_score=0.6 + (day * 0.04),
                    created_at=datetime.combine(
                        base_date + timedelta(days=day),
                        datetime.min.time()
                    ) + timedelta(hours=12)
                )
                operations.append(operation)
        
        self.session.add_all(operations)
        self.session.commit()
        
        # Mock MCP tool call
        mcp_params = {
            "user_id": str(self.test_user.id),
            "app_id": str(self.test_app.id),
            "days": 7
        }
        
        # Simulate MCP tool execution
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_evolution_metrics.return_value = {
                "total_operations": len(operations),
                "operations_by_type": {
                    "ADD": 7,
                    "UPDATE": 7,
                    "DELETE": 7,
                    "NOOP": 7
                },
                "average_confidence": 0.85,
                "average_similarity": 0.78,
                "learning_efficiency": 0.92,
                "period_start": base_date.isoformat(),
                "period_end": (base_date + timedelta(days=6)).isoformat()
            }
            
            # Test tool execution
            result = mock_instance.get_evolution_metrics(
                user_id=uuid.UUID(mcp_params["user_id"]),
                app_id=uuid.UUID(mcp_params["app_id"]),
                days=mcp_params["days"]
            )
            
            # Verify tool was called correctly
            mock_instance.get_evolution_metrics.assert_called_once_with(
                user_id=uuid.UUID(mcp_params["user_id"]),
                app_id=uuid.UUID(mcp_params["app_id"]),
                days=mcp_params["days"]
            )
            
            # Verify result structure
            assert "total_operations" in result
            assert "operations_by_type" in result
            assert "average_confidence" in result
            assert "learning_efficiency" in result
            assert result["total_operations"] == len(operations)
    
    def test_mcp_tool_get_evolution_insights(self):
        """Test MCP tool for getting evolution insights."""
        # Create test insights
        insights = []
        base_date = datetime.now().date() - timedelta(days=14)
        
        for day in range(14):
            insight_date = base_date + timedelta(days=day)
            insight = EvolutionInsight(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                date=insight_date,
                total_operations=10 + (day % 5),
                add_operations=3 + (day % 3),
                update_operations=3 + (day % 2),
                delete_operations=2 + (day % 2),
                noop_operations=2 + (day % 3),
                learning_efficiency=0.7 + (day * 0.02),
                conflict_resolution_count=day % 3,
                average_confidence=0.75 + (day * 0.01),
                average_similarity=0.70 + (day * 0.015)
            )
            insights.append(insight)
        
        self.session.add_all(insights)
        self.session.commit()
        
        # Mock MCP tool call
        mcp_params = {
            "user_id": str(self.test_user.id),
            "app_id": str(self.test_app.id),
            "start_date": base_date.isoformat(),
            "end_date": (base_date + timedelta(days=13)).isoformat()
        }
        
        # Simulate MCP tool execution
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_evolution_insights.return_value = [
                {
                    "date": insight.date.isoformat(),
                    "total_operations": insight.total_operations,
                    "learning_efficiency": insight.learning_efficiency,
                    "average_confidence": insight.average_confidence,
                    "operations_breakdown": {
                        "add": insight.add_operations,
                        "update": insight.update_operations,
                        "delete": insight.delete_operations,
                        "noop": insight.noop_operations
                    }
                }
                for insight in insights
            ]
            
            # Test tool execution
            result = mock_instance.get_evolution_insights(
                user_id=uuid.UUID(mcp_params["user_id"]),
                app_id=uuid.UUID(mcp_params["app_id"]),
                start_date=datetime.fromisoformat(mcp_params["start_date"]).date(),
                end_date=datetime.fromisoformat(mcp_params["end_date"]).date()
            )
            
            # Verify tool was called correctly
            mock_instance.get_evolution_insights.assert_called_once()
            
            # Verify result structure
            assert isinstance(result, list)
            assert len(result) == len(insights)
            
            for insight_data in result:
                assert "date" in insight_data
                assert "total_operations" in insight_data
                assert "learning_efficiency" in insight_data
                assert "operations_breakdown" in insight_data
    
    def test_mcp_tool_analyze_evolution_trends(self):
        """Test MCP tool for analyzing evolution trends."""
        # Create trend data
        base_date = datetime.now().date() - timedelta(days=30)
        
        # Create operations with trending patterns
        operations = []
        for day in range(30):
            # Simulate increasing confidence over time
            confidence_base = 0.6 + (day / 30) * 0.3  # 0.6 to 0.9
            
            for op_num in range(5 + (day % 3)):  # Varying operations per day
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=self.test_user.id,
                    app_id=self.test_app.id,
                    operation_type=EvolutionOperationType((op_num % 4) + 1),
                    candidate_fact=f"Trend analysis fact {day}-{op_num}",
                    confidence_score=confidence_base + (op_num * 0.02),
                    reasoning=f"Trend analysis reasoning {day}-{op_num}",
                    similarity_score=0.5 + (day / 30) * 0.4,
                    created_at=datetime.combine(
                        base_date + timedelta(days=day),
                        datetime.min.time()
                    ) + timedelta(hours=op_num * 2)
                )
                operations.append(operation)
        
        self.session.add_all(operations)
        self.session.commit()
        
        # Mock MCP tool call
        mcp_params = {
            "user_id": str(self.test_user.id),
            "app_id": str(self.test_app.id),
            "analysis_period": "30d",
            "trend_metrics": ["confidence", "similarity", "operation_count"]
        }
        
        # Simulate MCP tool execution
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.analyze_evolution_trends.return_value = {
                "period": "30d",
                "trends": {
                    "confidence": {
                        "direction": "increasing",
                        "slope": 0.01,
                        "correlation": 0.85,
                        "start_value": 0.62,
                        "end_value": 0.88
                    },
                    "similarity": {
                        "direction": "increasing",
                        "slope": 0.013,
                        "correlation": 0.78,
                        "start_value": 0.52,
                        "end_value": 0.87
                    },
                    "operation_count": {
                        "direction": "stable",
                        "slope": 0.02,
                        "correlation": 0.15,
                        "average_daily": 6.2
                    }
                },
                "insights": [
                    "Learning efficiency is improving over time",
                    "Confidence scores show consistent upward trend",
                    "Operation volume remains stable"
                ],
                "recommendations": [
                    "Continue current learning patterns",
                    "Monitor for potential overfitting"
                ]
            }
            
            # Test tool execution
            result = mock_instance.analyze_evolution_trends(
                user_id=uuid.UUID(mcp_params["user_id"]),
                app_id=uuid.UUID(mcp_params["app_id"]),
                analysis_period=mcp_params["analysis_period"],
                trend_metrics=mcp_params["trend_metrics"]
            )
            
            # Verify tool was called correctly
            mock_instance.analyze_evolution_trends.assert_called_once()
            
            # Verify result structure
            assert "period" in result
            assert "trends" in result
            assert "insights" in result
            assert "recommendations" in result
            
            # Verify trend data structure
            for metric in mcp_params["trend_metrics"]:
                assert metric in result["trends"]
                trend_data = result["trends"][metric]
                assert "direction" in trend_data
                assert "slope" in trend_data
                assert "correlation" in trend_data
    
    def test_mcp_tool_optimize_evolution_parameters(self):
        """Test MCP tool for optimizing evolution parameters."""
        # Create performance data for optimization
        operations = []
        base_date = datetime.now() - timedelta(days=7)
        
        # Create operations with varying performance characteristics
        for day in range(7):
            for scenario in range(10):
                # Simulate different parameter combinations
                confidence_threshold = 0.7 + (scenario * 0.02)
                similarity_threshold = 0.6 + (scenario * 0.03)
                
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=self.test_user.id,
                    app_id=self.test_app.id,
                    operation_type=EvolutionOperationType((scenario % 4) + 1),
                    candidate_fact=f"Optimization test fact {day}-{scenario}",
                    confidence_score=confidence_threshold + (scenario * 0.01),
                    reasoning=f"Optimization test reasoning {day}-{scenario}",
                    similarity_score=similarity_threshold + (scenario * 0.015),
                    metadata={
                        "confidence_threshold": confidence_threshold,
                        "similarity_threshold": similarity_threshold,
                        "scenario": scenario
                    }
                )
                operations.append(operation)
        
        self.session.add_all(operations)
        self.session.commit()
        
        # Mock MCP tool call
        mcp_params = {
            "user_id": str(self.test_user.id),
            "app_id": str(self.test_app.id),
            "optimization_target": "learning_efficiency",
            "parameter_ranges": {
                "confidence_threshold": {"min": 0.7, "max": 0.9},
                "similarity_threshold": {"min": 0.6, "max": 0.8},
                "batch_size": {"min": 10, "max": 50}
            }
        }
        
        # Simulate MCP tool execution
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.optimize_evolution_parameters.return_value = {
                "optimization_target": "learning_efficiency",
                "current_performance": 0.78,
                "optimized_parameters": {
                    "confidence_threshold": 0.82,
                    "similarity_threshold": 0.71,
                    "batch_size": 25
                },
                "predicted_performance": 0.89,
                "improvement_estimate": 0.11,
                "confidence_interval": [0.85, 0.93],
                "optimization_method": "bayesian_optimization",
                "iterations": 50,
                "validation_metrics": {
                    "cross_validation_score": 0.87,
                    "stability_score": 0.92,
                    "robustness_score": 0.84
                },
                "recommendations": [
                    "Apply optimized parameters gradually",
                    "Monitor performance for 7 days",
                    "Re-optimize if performance degrades"
                ]
            }
            
            # Test tool execution
            result = mock_instance.optimize_evolution_parameters(
                user_id=uuid.UUID(mcp_params["user_id"]),
                app_id=uuid.UUID(mcp_params["app_id"]),
                optimization_target=mcp_params["optimization_target"],
                parameter_ranges=mcp_params["parameter_ranges"]
            )
            
            # Verify tool was called correctly
            mock_instance.optimize_evolution_parameters.assert_called_once()
            
            # Verify result structure
            assert "optimization_target" in result
            assert "current_performance" in result
            assert "optimized_parameters" in result
            assert "predicted_performance" in result
            assert "improvement_estimate" in result
            assert "validation_metrics" in result
            assert "recommendations" in result
            
            # Verify optimization results are reasonable
            assert result["predicted_performance"] > result["current_performance"]
            assert result["improvement_estimate"] > 0
            assert 0 <= result["validation_metrics"]["cross_validation_score"] <= 1
    
    def test_mcp_tool_parameter_validation(self):
        """Test MCP tool parameter validation."""
        # Test invalid user_id
        with pytest.raises(ValueError, match="Invalid user_id format"):
            with patch('app.services.evolution_service.EvolutionService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.get_evolution_metrics(
                    user_id="invalid-uuid",
                    app_id=str(self.test_app.id),
                    days=7
                )
        
        # Test invalid app_id
        with pytest.raises(ValueError, match="Invalid app_id format"):
            with patch('app.services.evolution_service.EvolutionService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.get_evolution_metrics(
                    user_id=str(self.test_user.id),
                    app_id="invalid-uuid",
                    days=7
                )
        
        # Test invalid days parameter
        with pytest.raises(ValueError, match="Days must be positive"):
            with patch('app.services.evolution_service.EvolutionService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.get_evolution_metrics(
                    user_id=str(self.test_user.id),
                    app_id=str(self.test_app.id),
                    days=-1
                )
        
        # Test missing required parameters
        with pytest.raises(TypeError):
            with patch('app.services.evolution_service.EvolutionService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.get_evolution_metrics(
                    user_id=str(self.test_user.id)
                    # Missing app_id and days
                )
    
    def test_mcp_tool_error_handling(self):
        """Test MCP tool error handling."""
        # Test database connection error
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_evolution_metrics.side_effect = Exception("Database connection failed")
            
            with pytest.raises(Exception, match="Database connection failed"):
                mock_instance.get_evolution_metrics(
                    user_id=uuid.UUID(str(self.test_user.id)),
                    app_id=uuid.UUID(str(self.test_app.id)),
                    days=7
                )
        
        # Test non-existent user
        non_existent_user_id = uuid.uuid4()
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_evolution_metrics.return_value = {
                "error": "User not found",
                "user_id": str(non_existent_user_id)
            }
            
            result = mock_instance.get_evolution_metrics(
                user_id=non_existent_user_id,
                app_id=uuid.UUID(str(self.test_app.id)),
                days=7
            )
            
            assert "error" in result
            assert result["error"] == "User not found"
        
        # Test non-existent app
        non_existent_app_id = uuid.uuid4()
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            mock_instance.get_evolution_metrics.return_value = {
                "error": "App not found",
                "app_id": str(non_existent_app_id)
            }
            
            result = mock_instance.get_evolution_metrics(
                user_id=uuid.UUID(str(self.test_user.id)),
                app_id=non_existent_app_id,
                days=7
            )
            
            assert "error" in result
            assert result["error"] == "App not found"
    
    def test_mcp_tool_response_format(self):
        """Test MCP tool response format compliance."""
        # Test that all MCP tools return properly formatted JSON responses
        
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            
            # Test metrics tool response format
            mock_instance.get_evolution_metrics.return_value = {
                "total_operations": 100,
                "operations_by_type": {"ADD": 25, "UPDATE": 30, "DELETE": 20, "NOOP": 25},
                "average_confidence": 0.85,
                "average_similarity": 0.78,
                "learning_efficiency": 0.92
            }
            
            result = mock_instance.get_evolution_metrics(
                user_id=uuid.UUID(str(self.test_user.id)),
                app_id=uuid.UUID(str(self.test_app.id)),
                days=7
            )
            
            # Verify JSON serializable
            json_str = json.dumps(result)
            parsed_result = json.loads(json_str)
            assert parsed_result == result
            
            # Verify required fields
            required_fields = [
                "total_operations", "operations_by_type", 
                "average_confidence", "learning_efficiency"
            ]
            for field in required_fields:
                assert field in result
            
            # Verify data types
            assert isinstance(result["total_operations"], int)
            assert isinstance(result["operations_by_type"], dict)
            assert isinstance(result["average_confidence"], (int, float))
            assert isinstance(result["learning_efficiency"], (int, float))
    
    def test_mcp_tool_performance_requirements(self):
        """Test that MCP tools meet performance requirements."""
        import time
        
        # Create test data
        operations = []
        for i in range(100):
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                operation_type=EvolutionOperationType((i % 4) + 1),
                candidate_fact=f"Performance test fact {i}",
                confidence_score=0.7 + (i % 20) * 0.01,
                reasoning=f"Performance test reasoning {i}",
                similarity_score=0.6 + (i % 25) * 0.012
            )
            operations.append(operation)
        
        self.session.add_all(operations)
        self.session.commit()
        
        # Test MCP tool response time
        with patch('app.services.evolution_service.EvolutionService') as mock_service:
            mock_instance = mock_service.return_value
            
            # Simulate realistic processing time
            def mock_get_metrics(*args, **kwargs):
                time.sleep(0.05)  # 50ms processing time
                return {
                    "total_operations": 100,
                    "operations_by_type": {"ADD": 25, "UPDATE": 25, "DELETE": 25, "NOOP": 25},
                    "average_confidence": 0.85,
                    "learning_efficiency": 0.90
                }
            
            mock_instance.get_evolution_metrics.side_effect = mock_get_metrics
            
            # Measure response time
            start_time = time.perf_counter()
            
            result = mock_instance.get_evolution_metrics(
                user_id=uuid.UUID(str(self.test_user.id)),
                app_id=uuid.UUID(str(self.test_app.id)),
                days=7
            )
            
            end_time = time.perf_counter()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            print(f"MCP tool response time: {response_time:.2f}ms")
            
            # Verify performance requirement (should be <200ms)
            assert response_time < 200, f"MCP tool response time {response_time:.2f}ms exceeds 200ms requirement"
            
            # Verify result is valid
            assert "total_operations" in result
            assert result["total_operations"] == 100
    
    def test_mcp_tool_concurrent_access(self):
        """Test MCP tools under concurrent access."""
        import threading
        import time
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        # Create test data
        operations = []
        for i in range(50):
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                operation_type=EvolutionOperationType((i % 4) + 1),
                candidate_fact=f"Concurrent test fact {i}",
                confidence_score=0.7 + (i % 20) * 0.01,
                reasoning=f"Concurrent test reasoning {i}",
                similarity_score=0.6 + (i % 25) * 0.012
            )
            operations.append(operation)
        
        self.session.add_all(operations)
        self.session.commit()
        
        def call_mcp_tool(thread_id):
            """Simulate concurrent MCP tool calls."""
            with patch('app.services.evolution_service.EvolutionService') as mock_service:
                mock_instance = mock_service.return_value
                mock_instance.get_evolution_metrics.return_value = {
                    "total_operations": 50,
                    "thread_id": thread_id,
                    "timestamp": time.time()
                }
                
                return mock_instance.get_evolution_metrics(
                    user_id=uuid.UUID(str(self.test_user.id)),
                    app_id=uuid.UUID(str(self.test_app.id)),
                    days=7
                )
        
        # Test concurrent access
        concurrent_calls = 5
        start_time = time.perf_counter()
        
        with ThreadPoolExecutor(max_workers=concurrent_calls) as executor:
            futures = [executor.submit(call_mcp_tool, i) for i in range(concurrent_calls)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.perf_counter()
        total_time = (end_time - start_time) * 1000
        
        print(f"Concurrent MCP tool calls completed in: {total_time:.2f}ms")
        
        # Verify all calls completed successfully
        assert len(results) == concurrent_calls
        
        # Verify each result is valid
        for result in results:
            assert "total_operations" in result
            assert "thread_id" in result
            assert "timestamp" in result
        
        # Verify reasonable performance under concurrent load
        avg_time_per_call = total_time / concurrent_calls
        assert avg_time_per_call < 500, f"Average time per concurrent call {avg_time_per_call:.2f}ms exceeds threshold"