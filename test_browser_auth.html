<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Browser Authentication</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Browser Authentication</h1>
    
    <div>
        <h2>Login Form</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="Ahayh@5096$">
        <button onclick="testLogin()">Test Login</button>
    </div>
    
    <div>
        <h2>Results</h2>
        <div id="results"></div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'http://*************:8000';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            const results = document.getElementById('results');
            results.innerHTML += '<p>' + JSON.stringify(message, null, 2) + '</p>';
            console.log(message);
        }
        
        async function testValidateLogin(email, password) {
            try {
                log('Testing validate-login endpoint...');
                const response = await fetch('http://localhost:8765/auth/validate-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });

                const data = await response.json();
                log('Validate Login Response: ' + response.status + ' - ' + JSON.stringify(data));
                return response.ok;
            } catch (error) {
                log('Validate Login Error: ' + error.message);
                return false;
            }
        }
        
        async function testSupabaseAuth(email, password) {
            try {
                log('Testing Supabase authentication...');
                const { data, error } = await supabase.auth.signInWithPassword({
                    email,
                    password
                });
                
                if (error) {
                    log('Supabase Auth Error: ' + JSON.stringify(error));
                    return false;
                } else {
                    log('Supabase Auth Success: User ID = ' + data.user.id);
                    return data;
                }
            } catch (error) {
                log('Supabase Test Error: ' + error.message);
                return false;
            }
        }
        
        async function testLoginSuccess(email, session) {
            try {
                log('Testing login-success endpoint...');
                const response = await fetch('http://localhost:8765/auth/login-success', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${session.access_token}`,
                    },
                    body: JSON.stringify({ email }),
                });

                const data = await response.json();
                log('Login Success Response: ' + response.status + ' - ' + JSON.stringify(data));
                return response.ok;
            } catch (error) {
                log('Login Success Error: ' + error.message);
                return false;
            }
        }
        
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            document.getElementById('results').innerHTML = '';
            log('=== Starting Browser Authentication Test ===');
            
            // Step 1: Validate login
            const validateOk = await testValidateLogin(email, password);
            if (!validateOk) {
                log('❌ Validation failed, stopping test');
                return;
            }
            
            // Step 2: Supabase authentication
            const authResult = await testSupabaseAuth(email, password);
            if (!authResult) {
                log('❌ Supabase authentication failed, stopping test');
                return;
            }
            
            // Step 3: Record login success
            const successOk = await testLoginSuccess(email, authResult.session);
            if (!successOk) {
                log('❌ Login success recording failed');
                return;
            }
            
            log('✅ All tests passed! Authentication flow working correctly.');
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            log('Page loaded, ready to test authentication');
        });
    </script>
</body>
</html>
