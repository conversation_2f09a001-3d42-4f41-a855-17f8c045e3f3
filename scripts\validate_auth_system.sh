#!/bin/bash

# Memory Master Authentication System Validation Script
# This script validates that the authentication system is working correctly post-migration

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
API_URL="${API_URL:-http://localhost:8765}"
UI_URL="${UI_URL:-http://localhost:3000}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test result tracking
declare -a FAILED_TESTS=()

# Helper functions
log() {
    echo -e "${2:-$NC}[$(date '+%H:%M:%S')] $1${NC}"
}

error() {
    log "❌ $1" "$RED"
    FAILED_TESTS+=("$1")
    ((TESTS_FAILED++))
}

success() {
    log "✅ $1" "$GREEN"
    ((TESTS_PASSED++))
}

info() {
    log "ℹ️  $1" "$BLUE"
}

warning() {
    log "⚠️  $1" "$YELLOW"
}

run_test() {
    local test_name="$1"
    shift
    ((TESTS_RUN++))
    
    info "Running test: $test_name"
    
    if "$@"; then
        success "$test_name"
        return 0
    else
        error "$test_name"
        return 1
    fi
}

# Test functions
test_api_health() {
    curl -s -f "$API_URL/health" > /dev/null
}

test_ui_accessibility() {
    curl -s -f "$UI_URL" > /dev/null
}

test_auth_endpoints_exist() {
    # Test that auth endpoints exist (should return 400/401, not 404)
    local login_status=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_URL/auth/login")
    local logout_status=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_URL/auth/logout")
    
    [[ "$login_status" != "404" ]] && [[ "$logout_status" != "404" ]]
}

test_authentication_enabled() {
    # Check that authentication is actually enabled by testing protected endpoint
    local memories_status=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/memories")
    
    # Should return 401 (unauthorized) if auth is enabled, 200 if disabled
    [[ "$memories_status" == "401" ]]
}

test_cors_headers() {
    local response=$(curl -s -H "Origin: $UI_URL" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: Content-Type" -X OPTIONS "$API_URL/auth/login")
    
    # Should contain CORS headers
    curl -s -I -H "Origin: $UI_URL" "$API_URL/auth/login" | grep -q "Access-Control-Allow-Origin"
}

test_security_headers() {
    local headers=$(curl -s -I "$API_URL/health")
    
    echo "$headers" | grep -q "X-Frame-Options" && \
    echo "$headers" | grep -q "X-Content-Type-Options"
}

test_rate_limiting() {
    info "Testing rate limiting (this may take a moment)..."
    
    # Make multiple rapid requests to trigger rate limiting
    local count=0
    for i in {1..10}; do
        local status=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_URL/auth/login" \
            -H "Content-Type: application/json" \
            -d '{"email":"<EMAIL>","password":"wrong"}')
        
        if [[ "$status" == "429" ]]; then
            count=$((count + 1))
        fi
        
        sleep 0.1
    done
    
    # Should have hit rate limit at least once
    [[ $count -gt 0 ]]
}

test_session_timeout_config() {
    # Check that session timeout environment variables are set
    docker-compose exec -T api sh -c 'env | grep -E "(JWT_ACCESS_TOKEN_EXPIRY|JWT_REFRESH_TOKEN_EXPIRY)"' > /dev/null
}

test_database_connectivity() {
    # Test that API can connect to database
    curl -s -f "$API_URL/stats" > /dev/null
}

test_mcp_configuration() {
    # Test that MCP endpoints are working
    local mcp_status=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/mcp/openmemory/sse/test")
    
    # Should not return 404
    [[ "$mcp_status" != "404" ]]
}

test_ui_auth_integration() {
    # Test that UI has auth components
    local ui_content=$(curl -s "$UI_URL/auth/login")
    
    echo "$ui_content" | grep -q -i "login\|email\|password"
}

test_environment_variables() {
    # Check that legacy environment variables are removed
    local ui_has_legacy=$(docker-compose exec -T ui sh -c 'env | grep NEXT_PUBLIC_USER_ID' 2>/dev/null | wc -l)
    local api_has_legacy=$(docker-compose exec -T api sh -c 'env | grep ^USER=' 2>/dev/null | wc -l)
    
    [[ $ui_has_legacy -eq 0 ]] && [[ $api_has_legacy -eq 0 ]]
}

test_supabase_configuration() {
    # Check that Supabase environment variables are set
    docker-compose exec -T api sh -c 'env | grep -E "(SUPABASE_URL|SUPABASE_SERVICE_ROLE_KEY|SUPABASE_JWT_SECRET)"' > /dev/null && \
    docker-compose exec -T ui sh -c 'env | grep -E "(NEXT_PUBLIC_SUPABASE_URL|NEXT_PUBLIC_SUPABASE_ANON_KEY)"' > /dev/null
}

test_feature_flags_enabled() {
    # Check that auth feature flags are enabled
    local api_auth=$(docker-compose exec -T api sh -c 'env | grep AUTH_ENABLED=true' | wc -l)
    local ui_auth=$(docker-compose exec -T ui sh -c 'env | grep NEXT_PUBLIC_AUTH_ENABLED=true' | wc -l)
    
    [[ $api_auth -gt 0 ]] && [[ $ui_auth -gt 0 ]]
}

# Performance tests
test_api_response_time() {
    local start_time=$(date +%s%N)
    curl -s -f "$API_URL/health" > /dev/null
    local end_time=$(date +%s%N)
    
    local duration_ms=$(( (end_time - start_time) / 1000000 ))
    
    info "API response time: ${duration_ms}ms"
    
    # Should respond within 2 seconds (2000ms)
    [[ $duration_ms -lt 2000 ]]
}

test_ui_load_time() {
    local start_time=$(date +%s%N)
    curl -s -f "$UI_URL" > /dev/null
    local end_time=$(date +%s%N)
    
    local duration_ms=$(( (end_time - start_time) / 1000000 ))
    
    info "UI load time: ${duration_ms}ms"
    
    # Should load within 5 seconds (5000ms)
    [[ $duration_ms -lt 5000 ]]
}

# Main validation function
run_validation() {
    log "🚀 Starting Memory Master Authentication System Validation" "$BLUE"
    log "API URL: $API_URL" "$BLUE"
    log "UI URL: $UI_URL" "$BLUE"
    echo ""
    
    # Basic health checks
    log "🏥 Basic Health Checks" "$YELLOW"
    run_test "API Health Check" test_api_health
    run_test "UI Accessibility" test_ui_accessibility
    run_test "Database Connectivity" test_database_connectivity
    echo ""
    
    # Authentication system tests
    log "🔐 Authentication System Tests" "$YELLOW"
    run_test "Auth Endpoints Exist" test_auth_endpoints_exist
    run_test "Authentication Enabled" test_authentication_enabled
    run_test "CORS Headers Present" test_cors_headers
    run_test "Feature Flags Enabled" test_feature_flags_enabled
    echo ""
    
    # Security tests
    log "🛡️  Security Tests" "$YELLOW"
    run_test "Security Headers" test_security_headers
    run_test "Rate Limiting" test_rate_limiting
    run_test "Session Timeout Config" test_session_timeout_config
    echo ""
    
    # Configuration tests
    log "⚙️  Configuration Tests" "$YELLOW"
    run_test "Supabase Configuration" test_supabase_configuration
    run_test "Legacy Variables Removed" test_environment_variables
    run_test "MCP Configuration" test_mcp_configuration
    run_test "UI Auth Integration" test_ui_auth_integration
    echo ""
    
    # Performance tests
    log "⚡ Performance Tests" "$YELLOW"
    run_test "API Response Time" test_api_response_time
    run_test "UI Load Time" test_ui_load_time
    echo ""
    
    # Results summary
    log "📊 Validation Results" "$BLUE"
    log "Tests Run: $TESTS_RUN"
    log "Tests Passed: $TESTS_PASSED" "$GREEN"
    log "Tests Failed: $TESTS_FAILED" "$RED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        echo ""
        log "❌ Failed Tests:" "$RED"
        for test in "${FAILED_TESTS[@]}"; do
            log "  - $test" "$RED"
        done
        echo ""
        log "🔧 Please review the failed tests and fix any issues before proceeding" "$YELLOW"
        return 1
    else
        echo ""
        log "🎉 All tests passed! Authentication system is working correctly" "$GREEN"
        return 0
    fi
}

# Help function
show_help() {
    cat << EOF
Memory Master Authentication System Validation Script

This script validates that the authentication system is working correctly after migration.

Usage: $0 [OPTIONS]

OPTIONS:
    --api-url URL       API base URL (default: http://localhost:8765)
    --ui-url URL        UI base URL (default: http://localhost:3000)
    --help              Show this help message

Environment Variables:
    API_URL             Override default API URL
    UI_URL              Override default UI URL

Examples:
    $0                                          # Run with defaults
    $0 --api-url https://api.example.com       # Custom API URL
    $0 --ui-url https://app.example.com        # Custom UI URL

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --api-url)
            API_URL="$2"
            shift 2
            ;;
        --ui-url)
            UI_URL="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log "Unknown option: $1" "$RED"
            show_help
            exit 1
            ;;
    esac
done

# Run validation
if run_validation; then
    exit 0
else
    exit 1
fi