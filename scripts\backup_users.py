#!/usr/bin/env python3
"""
User Data Backup and <PERSON><PERSON> Script for Memory Master

This script provides utilities for backing up and restoring user data.

Usage:
    # Backup all users
    python scripts/backup_users.py --backup --output backup_users.json
    
    # Backup specific user
    python scripts/backup_users.py --backup --email <EMAIL> --output user_backup.json
    
    # List backed up data
    python scripts/backup_users.py --list-backup backup_users.json
    
    # Restore users (dry run)
    python scripts/backup_users.py --restore backup_users.json --dry-run
    
    # Restore users (actual)
    python scripts/backup_users.py --restore backup_users.json

Environment Variables Required:
    SUPABASE_URL - Your Supabase project URL
    SUPABASE_SERVICE_ROLE_KEY - Your Supabase service role key
"""

import os
import sys
import argparse
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

try:
    from supabase import create_client, Client
    from supabase.client import AuthAPIError
except ImportError:
    print("Error: Supabase library not installed. Run: pip install supabase")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UserDataManager:
    def __init__(self):
        """Initialize the UserDataManager with Supabase client."""
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not self.supabase_url or not self.service_key:
            raise ValueError(
                "Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY"
            )
        
        self.supabase: Client = create_client(self.supabase_url, self.service_key)
        logger.info("✅ Supabase client initialized successfully")

    def backup_users(self, email_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Backup user data from Supabase Auth.
        
        Args:
            email_filter: If provided, only backup this specific user
            
        Returns:
            Dictionary containing backup data
        """
        try:
            logger.info("Starting user data backup...")
            
            # Get all users from Supabase Auth
            result = self.supabase.auth.admin.list_users()
            users = result.users
            
            backup_data = {
                'backup_timestamp': datetime.utcnow().isoformat(),
                'backup_source': 'supabase_auth',
                'total_users': 0,
                'users': []
            }
            
            for user in users:
                # Filter by email if specified
                if email_filter and user.email != email_filter:
                    continue
                
                user_data = {
                    'id': user.id,
                    'email': user.email,
                    'email_confirmed_at': user.email_confirmed_at,
                    'created_at': user.created_at,
                    'updated_at': user.updated_at,
                    'last_sign_in_at': user.last_sign_in_at,
                    'user_metadata': user.user_metadata,
                    'app_metadata': user.app_metadata,
                    'aud': getattr(user, 'aud', None),
                    'role': getattr(user, 'role', None)
                }
                
                backup_data['users'].append(user_data)
            
            backup_data['total_users'] = len(backup_data['users'])
            
            if email_filter:
                logger.info(f"✅ Backup completed for user: {email_filter}")
            else:
                logger.info(f"✅ Backup completed for {backup_data['total_users']} users")
            
            return backup_data
            
        except Exception as e:
            logger.error(f"❌ Error during backup: {e}")
            raise

    def save_backup_to_file(self, backup_data: Dict[str, Any], filename: str):
        """Save backup data to a JSON file."""
        try:
            with open(filename, 'w') as f:
                json.dump(backup_data, f, indent=2, default=str)
            
            logger.info(f"✅ Backup saved to: {filename}")
            
        except Exception as e:
            logger.error(f"❌ Error saving backup to file: {e}")
            raise

    def load_backup_from_file(self, filename: str) -> Dict[str, Any]:
        """Load backup data from a JSON file."""
        try:
            with open(filename, 'r') as f:
                backup_data = json.load(f)
            
            logger.info(f"✅ Backup loaded from: {filename}")
            return backup_data
            
        except FileNotFoundError:
            logger.error(f"❌ Backup file not found: {filename}")
            raise
        except Exception as e:
            logger.error(f"❌ Error loading backup from file: {e}")
            raise

    def list_backup_contents(self, backup_data: Dict[str, Any]):
        """Display the contents of a backup file."""
        print(f"\n📋 Backup Information:")
        print(f"   Timestamp: {backup_data.get('backup_timestamp', 'N/A')}")
        print(f"   Source: {backup_data.get('backup_source', 'N/A')}")
        print(f"   Total Users: {backup_data.get('total_users', 0)}")
        
        if backup_data.get('users'):
            print(f"\n👥 Users in backup:")
            print("-" * 80)
            print(f"{'Email':<35} {'User ID':<40}")
            print("-" * 80)
            
            for user in backup_data['users']:
                print(f"{user['email']:<35} {user['id']:<40}")
            
            print("-" * 80)

    def restore_users(self, backup_data: Dict[str, Any], dry_run: bool = True) -> Dict[str, Any]:
        """
        Restore users from backup data.
        
        Args:
            backup_data: The backup data to restore
            dry_run: If True, only simulate the restore process
            
        Returns:
            Dictionary with restore results
        """
        results = {
            'total_users': len(backup_data.get('users', [])),
            'would_create': [],
            'would_skip': [],
            'created': [],
            'skipped': [],
            'errors': []
        }
        
        try:
            # Get existing users to check for duplicates
            existing_result = self.supabase.auth.admin.list_users()
            existing_emails = {user.email for user in existing_result.users}
            
            for user_data in backup_data.get('users', []):
                email = user_data['email']
                
                if email in existing_emails:
                    if dry_run:
                        results['would_skip'].append(email)
                        logger.info(f"[DRY RUN] Would skip existing user: {email}")
                    else:
                        results['skipped'].append(email)
                        logger.info(f"Skipping existing user: {email}")
                    continue
                
                if dry_run:
                    results['would_create'].append(email)
                    logger.info(f"[DRY RUN] Would create user: {email}")
                else:
                    # Actually create the user
                    try:
                        # Note: We can't restore passwords, so users will need to reset them
                        create_data = {
                            'email': email,
                            'email_confirm': user_data.get('email_confirmed_at') is not None,
                            'user_metadata': user_data.get('user_metadata', {}),
                            'app_metadata': user_data.get('app_metadata', {})
                        }
                        
                        result = self.supabase.auth.admin.create_user(create_data)
                        
                        if result.user:
                            results['created'].append(email)
                            logger.info(f"✅ Restored user: {email}")
                        else:
                            results['errors'].append({'email': email, 'error': 'No user object returned'})
                            
                    except Exception as e:
                        results['errors'].append({'email': email, 'error': str(e)})
                        logger.error(f"❌ Error restoring user {email}: {e}")
            
            mode = "DRY RUN" if dry_run else "ACTUAL RESTORE"
            logger.info(f"✅ Restore process completed [{mode}]")
            
        except Exception as e:
            logger.error(f"❌ Error during restore: {e}")
            raise
        
        return results

    def print_restore_summary(self, results: Dict[str, Any], dry_run: bool = True):
        """Print a summary of the restore process."""
        mode = "DRY RUN RESULTS" if dry_run else "RESTORE RESULTS"
        
        print(f"\n" + "="*60)
        print(f"{mode}")
        print("="*60)
        
        if dry_run:
            print(f"\n✅ Would create: {len(results['would_create'])} users")
            for email in results['would_create']:
                print(f"   • {email}")
            
            print(f"\n⏭️  Would skip: {len(results['would_skip'])} users (already exist)")
            for email in results['would_skip']:
                print(f"   • {email}")
        else:
            print(f"\n✅ Created: {len(results['created'])} users")
            for email in results['created']:
                print(f"   • {email}")
            
            print(f"\n⏭️  Skipped: {len(results['skipped'])} users (already exist)")
            for email in results['skipped']:
                print(f"   • {email}")
            
            if results['errors']:
                print(f"\n❌ Errors: {len(results['errors'])} users")
                for error in results['errors']:
                    print(f"   • {error['email']}: {error['error']}")
        
        print(f"\n📊 Total processed: {results['total_users']}")
        
        if not dry_run and results['created']:
            print("\n🔐 IMPORTANT:")
            print("• Restored users do not have passwords - they need to reset them")
            print("• User metadata and app metadata have been restored")
            print("• Original user IDs are not preserved (new IDs generated)")
        
        print("\n" + "="*60)

def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description='Memory Master User Data Backup/Restore')
    
    # Action arguments
    parser.add_argument('--backup', action='store_true', help='Backup user data')
    parser.add_argument('--restore', help='Restore from backup file')
    parser.add_argument('--list-backup', help='List contents of backup file')
    
    # Options
    parser.add_argument('--email', help='Email filter for backup (single user)')
    parser.add_argument('--output', help='Output file for backup')
    parser.add_argument('--dry-run', action='store_true', help='Dry run for restore (no actual changes)')
    
    args = parser.parse_args()
    
    if not any([args.backup, args.restore, args.list_backup]):
        parser.print_help()
        print("\nExamples:")
        print("  python scripts/backup_users.py --backup --output backup.json")
        print("  python scripts/backup_users.py --backup --email <EMAIL> --output user.json")
        print("  python scripts/backup_users.py --list-backup backup.json")
        print("  python scripts/backup_users.py --restore backup.json --dry-run")
        print("  python scripts/backup_users.py --restore backup.json")
        sys.exit(1)
    
    try:
        manager = UserDataManager()
        
        if args.backup:
            if not args.output:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                args.output = f"user_backup_{timestamp}.json"
            
            backup_data = manager.backup_users(args.email)
            manager.save_backup_to_file(backup_data, args.output)
            
            print(f"\n🎉 Backup completed successfully!")
            print(f"File: {args.output}")
            print(f"Users backed up: {backup_data['total_users']}")
        
        elif args.list_backup:
            backup_data = manager.load_backup_from_file(args.list_backup)
            manager.list_backup_contents(backup_data)
        
        elif args.restore:
            backup_data = manager.load_backup_from_file(args.restore)
            results = manager.restore_users(backup_data, dry_run=args.dry_run)
            manager.print_restore_summary(results, dry_run=args.dry_run)
            
            if args.dry_run:
                print("\n💡 Run without --dry-run to perform actual restore")
            elif results['errors']:
                sys.exit(1)
                
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("\n💡 Make sure to set the required environment variables:")
        print("   export SUPABASE_URL='your-supabase-url'")
        print("   export SUPABASE_SERVICE_ROLE_KEY='your-service-role-key'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()