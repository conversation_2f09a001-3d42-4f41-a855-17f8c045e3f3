"""
Tests for Configuration Hot-Reload Manager

This module contains comprehensive tests for the ConfigManager class
and its hot-reload functionality.
"""

import pytest
import threading
import time
import json
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from app.utils.config_manager import (
    ConfigManager, 
    ConfigChangeEvent, 
    ConfigChangeType, 
    ConfigValidationResult,
    get_config_manager
)
from app.utils.config_listeners import memory_client_config_listener


class TestConfigManager:
    """Test suite for ConfigManager class."""
    
    def setup_method(self):
        """Reset ConfigManager singleton before each test."""
        # Reset the singleton instance
        ConfigManager._instance = None
    
    def test_singleton_pattern(self):
        """Test that ConfigManager follows singleton pattern."""
        manager1 = ConfigManager.get_instance()
        manager2 = ConfigManager.get_instance()
        
        assert manager1 is manager2
        assert isinstance(manager1, ConfigManager)
    
    def test_get_config_manager_function(self):
        """Test the get_config_manager convenience function."""
        manager1 = get_config_manager()
        manager2 = get_config_manager()
        
        assert manager1 is manager2
        assert isinstance(manager1, ConfigManager)
    
    @patch('app.utils.config_manager.SessionLocal')
    def test_initial_config_loading(self, mock_session_local):
        """Test that configuration is loaded from database on initialization."""
        # Mock database session and config
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        mock_config = Mock()
        mock_config.value = {
            "mem0": {
                "llm": {"provider": "openai", "config": {"model": "gpt-4"}}
            }
        }
        mock_db.query.return_value.filter.return_value.first.return_value = mock_config
        
        # Create ConfigManager instance
        manager = ConfigManager.get_instance()
        
        # Verify config was loaded
        config = manager.get_config()
        assert "mem0" in config
        assert config["mem0"]["llm"]["provider"] == "openai"
    
    def test_get_config_value_with_dot_notation(self):
        """Test getting configuration values using dot notation."""
        manager = ConfigManager.get_instance()
        
        # Set up test configuration
        test_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "model": "gpt-4",
                        "temperature": 0.7
                    }
                }
            }
        }
        
        with manager._config_lock:
            manager._config = test_config
        
        # Test getting nested values
        assert manager.get_config_value("mem0.llm.config.model") == "gpt-4"
        assert manager.get_config_value("mem0.llm.config.temperature") == 0.7
        assert manager.get_config_value("nonexistent.key", "default") == "default"
    
    def test_config_validation_success(self):
        """Test successful configuration validation."""
        manager = ConfigManager.get_instance()
        
        valid_config = {
            "mem0": {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4",
                        "temperature": 0.7,
                        "max_tokens": 2000,
                        "api_key": "sk-test123"
                    }
                }
            },
            "openmemory": {
                "max_text_length": 1500
            }
        }
        
        result = manager._validate_config(valid_config)
        
        assert result.valid is True
        assert len(result.errors) == 0
    
    def test_config_validation_errors(self):
        """Test configuration validation with errors."""
        manager = ConfigManager.get_instance()
        
        invalid_config = {
            "mem0": {
                "llm": {
                    "provider": "",  # Empty provider
                    "config": {
                        "temperature": 1.5,  # Invalid temperature
                        "max_tokens": -100,  # Invalid max_tokens
                        "api_key": ""  # Empty API key
                    }
                }
            },
            "openmemory": {
                "max_text_length": -50  # Invalid max_text_length
            }
        }
        
        result = manager._validate_config(invalid_config)
        
        assert result.valid is False
        assert len(result.errors) > 0
        
        # Check for specific error messages
        error_messages = " ".join(result.errors)
        assert "provider cannot be empty" in error_messages
        assert "temperature must be a number between 0 and 1" in error_messages
        assert "max_tokens must be a positive integer" in error_messages
    
    def test_critical_vs_non_critical_changes(self):
        """Test detection of critical vs non-critical configuration changes."""
        manager = ConfigManager.get_instance()
        
        old_config = {
            "mem0": {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4",
                        "temperature": 0.7,
                        "api_key": "old-key"
                    }
                }
            }
        }
        
        # Non-critical change (temperature)
        non_critical_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "temperature": 0.8
                    }
                }
            }
        }
        
        change_type, requires_restart = manager._analyze_config_changes(old_config, non_critical_config)
        assert change_type == ConfigChangeType.NON_CRITICAL
        assert requires_restart is False
        
        # Critical change (API key)
        critical_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "api_key": "new-key"
                    }
                }
            }
        }
        
        change_type, requires_restart = manager._analyze_config_changes(old_config, critical_config)
        assert change_type == ConfigChangeType.CRITICAL
        assert requires_restart is True
    
    @patch('app.utils.config_manager.SessionLocal')
    def test_update_config_success(self, mock_session_local):
        """Test successful configuration update."""
        # Mock database operations
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        manager = ConfigManager.get_instance()
        
        # Set initial config
        with manager._config_lock:
            manager._config = {
                "mem0": {
                    "llm": {
                        "config": {
                            "temperature": 0.7
                        }
                    }
                }
            }
        
        # Update configuration
        new_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "temperature": 0.8
                    }
                }
            }
        }
        
        result = manager.update_config(new_config)
        
        assert result["success"] is True
        assert result["requires_restart"] is False
        assert result["change_type"] == "non_critical"
        assert result["config_version"] > 0
    
    def test_update_config_validation_error(self):
        """Test configuration update with validation error."""
        manager = ConfigManager.get_instance()
        
        invalid_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "temperature": 2.0  # Invalid temperature
                    }
                }
            }
        }
        
        with pytest.raises(ValueError) as exc_info:
            manager.update_config(invalid_config)
        
        assert "Invalid configuration" in str(exc_info.value)
    
    def test_change_listeners(self):
        """Test configuration change listener functionality."""
        manager = ConfigManager.get_instance()
        
        # Mock listener
        listener_called = threading.Event()
        received_event = None
        
        def test_listener(event: ConfigChangeEvent):
            nonlocal received_event
            received_event = event
            listener_called.set()
        
        # Add listener
        manager.add_change_listener(test_listener)
        
        # Set initial config and trigger change
        with manager._config_lock:
            manager._config = {"test": "initial"}
            manager._config_version = 1
        
        # Update config (this should trigger listener)
        new_config = {"test": "updated"}
        
        with patch('app.utils.config_manager.SessionLocal'):
            manager.update_config(new_config, save_to_db=False)
        
        # Wait for listener to be called
        assert listener_called.wait(timeout=1.0)
        assert received_event is not None
        assert received_event.new_config["test"] == "updated"
        
        # Remove listener
        manager.remove_change_listener(test_listener)
    
    def test_thread_safety(self):
        """Test thread safety of ConfigManager operations."""
        manager = ConfigManager.get_instance()
        
        # Set initial config
        with manager._config_lock:
            manager._config = {"counter": 0}
        
        results = []
        errors = []
        
        def update_config_worker(worker_id: int):
            try:
                for i in range(10):
                    config = manager.get_config()
                    new_value = config.get("counter", 0) + 1
                    
                    with patch('app.utils.config_manager.SessionLocal'):
                        manager.update_config({"counter": new_value}, save_to_db=False)
                    
                    time.sleep(0.001)  # Small delay to increase chance of race conditions
                
                results.append(f"Worker {worker_id} completed")
                
            except Exception as e:
                errors.append(f"Worker {worker_id} error: {e}")
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=update_config_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=5.0)
        
        # Check results
        assert len(errors) == 0, f"Thread safety errors: {errors}"
        assert len(results) == 5
        
        # Final config should be consistent
        final_config = manager.get_config()
        assert "counter" in final_config
        assert isinstance(final_config["counter"], int)
    
    def test_config_versioning(self):
        """Test configuration versioning functionality."""
        manager = ConfigManager.get_instance()
        
        initial_version = manager.get_version()
        
        # Update configuration multiple times
        for i in range(3):
            with patch('app.utils.config_manager.SessionLocal'):
                manager.update_config({f"test_{i}": f"value_{i}"}, save_to_db=False)
        
        final_version = manager.get_version()
        
        # Version should have incremented
        assert final_version > initial_version
        assert final_version == initial_version + 3
    
    def test_reset_functionality(self):
        """Test configuration manager reset functionality."""
        manager = ConfigManager.get_instance()
        
        # Set some configuration and add listener
        test_listener = Mock()
        manager.add_change_listener(test_listener)
        
        with patch('app.utils.config_manager.SessionLocal'):
            manager.update_config({"test": "value"}, save_to_db=False)
        
        initial_version = manager.get_version()
        assert initial_version > 0
        
        # Reset manager
        manager.reset()
        
        # Check that everything was reset
        assert manager.get_version() == 0
        assert manager.get_config() == {}
        assert len(manager._change_listeners) == 0
