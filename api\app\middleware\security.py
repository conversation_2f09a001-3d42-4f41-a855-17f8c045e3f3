"""
Security middleware for the FastAPI application.
"""
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import logging
import time
from typing import Callable
from app.utils.rate_limiter import rate_limiter, brute_force_protection, get_client_ip

logger = logging.getLogger(__name__)

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Security middleware that handles:
    - Rate limiting for authentication endpoints
    - Security headers
    - Request logging for security monitoring
    """
    
    def __init__(self, app, auth_rate_limit: int = 5, auth_window_minutes: int = 15):
        super().__init__(app)
        self.auth_rate_limit = auth_rate_limit
        self.auth_window_minutes = auth_window_minutes
        
        # Auth endpoints that need rate limiting
        self.auth_endpoints = {
            '/auth/login',
            '/auth/signup', 
            '/auth/reset-password',
            '/auth/verify-email'
        }
    
    async def dispatch(self, request: Request, call_next: Callable):
        start_time = time.time()
        client_ip = get_client_ip(request)
        
        # Add security headers to all responses
        response = await self._handle_request(request, call_next, client_ip)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "camera=(), microphone=(), geolocation=()"
        
        # Log request for security monitoring
        processing_time = time.time() - start_time
        self._log_request(request, response, client_ip, processing_time)
        
        return response
    
    async def _handle_request(self, request: Request, call_next: Callable, client_ip: str):
        """Handle the request with security checks."""
        
        # Check if this is an auth endpoint that needs rate limiting
        if any(request.url.path.startswith(endpoint) for endpoint in self.auth_endpoints):
            if self._is_rate_limited(client_ip, request.url.path):
                logger.warning(f"Rate limit exceeded for {client_ip} on {request.url.path}")
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "detail": "Too many requests. Please try again later.",
                        "retry_after": self.auth_window_minutes * 60
                    },
                    headers={"Retry-After": str(self.auth_window_minutes * 60)}
                )
        
        # Continue with the request
        response = await call_next(request)
        
        # Record successful auth endpoint access
        if any(request.url.path.startswith(endpoint) for endpoint in self.auth_endpoints):
            rate_limiter.record_attempt(client_ip)
        
        return response
    
    def _is_rate_limited(self, client_ip: str, endpoint: str) -> bool:
        """Check if the client IP is rate limited for auth endpoints."""
        return rate_limiter.is_rate_limited(
            f"auth_{client_ip}", 
            max_attempts=self.auth_rate_limit,
            window_minutes=self.auth_window_minutes
        )
    
    def _log_request(self, request: Request, response, client_ip: str, processing_time: float):
        """Log request for security monitoring."""
        log_data = {
            "timestamp": time.time(),
            "client_ip": client_ip,
            "method": request.method,
            "path": request.url.path,
            "status_code": getattr(response, 'status_code', 'unknown'),
            "processing_time": round(processing_time, 4),
            "user_agent": request.headers.get("user-agent", "unknown"),
            "referer": request.headers.get("referer", "none")
        }
        
        # Log at different levels based on status code
        if hasattr(response, 'status_code'):
            if response.status_code >= 500:
                logger.error(f"Server error: {log_data}")
            elif response.status_code >= 400:
                logger.warning(f"Client error: {log_data}")
            elif request.url.path.startswith('/auth/'):
                # Always log auth requests at info level
                logger.info(f"Auth request: {log_data}")
            else:
                logger.debug(f"Request: {log_data}")

class AuthSecurityMiddleware:
    """
    Middleware specifically for authentication security checks.
    This can be used as a dependency in auth endpoints.
    """
    
    @staticmethod
    async def check_brute_force(request: Request, identifier: str) -> dict:
        """
        Check for brute force attacks and return protection status.
        
        Args:
            request: The FastAPI request object
            identifier: User identifier (email, user_id, etc.)
            
        Returns:
            Dict with protection status
        """
        client_ip = get_client_ip(request)
        
        # Check both IP-based and user-based brute force protection
        ip_protection = brute_force_protection.check_and_record_failure(f"ip_{client_ip}")
        user_protection = brute_force_protection.check_and_record_failure(f"user_{identifier}")
        
        # If either IP or user is blocked, block the request
        if ip_protection['blocked'] or user_protection['blocked']:
            blocked_info = ip_protection if ip_protection['blocked'] else user_protection
            
            logger.warning(f"Brute force protection triggered for {identifier} from {client_ip}")
            
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Too many failed attempts",
                    "remaining_minutes": blocked_info['remaining_minutes'],
                    "message": f"Account temporarily locked due to too many failed attempts. Try again in {blocked_info['remaining_minutes']} minutes."
                }
            )
        
        return {
            "ip_remaining": ip_protection['remaining_attempts'],
            "user_remaining": user_protection['remaining_attempts']
        }
    
    @staticmethod
    def record_successful_auth(request: Request, identifier: str):
        """Record a successful authentication to reset brute force counters."""
        client_ip = get_client_ip(request)
        
        brute_force_protection.record_success(f"ip_{client_ip}")
        brute_force_protection.record_success(f"user_{identifier}")
        
        logger.info(f"Successful authentication for {identifier} from {client_ip}")

# Export instances for use in the main app
security_middleware = SecurityMiddleware
auth_security = AuthSecurityMiddleware()