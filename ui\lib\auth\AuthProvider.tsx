'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthChangeEvent } from '@supabase/supabase-js'
import { createClient } from '@/lib/supabase'
import { useDispatch } from 'react-redux'
import { setUserId, resetProfileState } from '@/store/profileSlice'

// Security logging function
const logAuthEvent = (event: string, userEmail: string) => {
  const logData = {
    timestamp: new Date().toISOString(),
    event,
    userEmail: userEmail.replace(/(.{2}).*@/, '$1***@'), // Partially mask email for privacy
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    ip: 'masked' // IP logging would require server-side implementation
  }
  
  // In production, send this to your logging service
  console.info('Auth Event:', logData)
  
  // Store recent auth events in sessionStorage for security dashboard
  if (typeof window !== 'undefined') {
    const recentEvents = JSON.parse(sessionStorage.getItem('auth_events') || '[]')
    recentEvents.push(logData)
    // Keep only last 10 events
    if (recentEvents.length > 10) {
      recentEvents.shift()
    }
    sessionStorage.setItem('auth_events', JSON.stringify(recentEvents))
  }
}

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string) => Promise<{ error: any }>
  signOut: () => Promise<{ error: any }>
  resetPassword: (email: string) => Promise<{ error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const dispatch = useDispatch()

  useEffect(() => {
    // Check if authentication is enabled
    const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
    
    if (!isAuthEnabled) {
      setLoading(false)
      return
    }

    const supabase = createClient()
    
    // Get initial session
    supabase.auth.getSession().then(({ data }: { data: { session: Session | null } }) => {
      setSession(data.session)
      setUser(data.session?.user ?? null)
      setLoading(false)

      // Log authentication event for security monitoring
      if (data.session?.user) {
        console.info('Session restored for user:', data.session.user.email)
        logAuthEvent('session_restored', data.session.user.email)
        // Update profile with authenticated user ID
        dispatch(setUserId(data.session.user.email))
      }
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event: AuthChangeEvent, session: Session | null) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)

      // Log authentication events for security monitoring
      if (session?.user) {
        console.info(`Auth event: ${event} for user:`, session.user.email)
        logAuthEvent(event, session.user.email)
        
        // Update profile with authenticated user ID
        dispatch(setUserId(session.user.email))
        
        // Set session timestamp for idle timeout tracking
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          document.cookie = `session-timestamp=${Date.now()}; path=/; secure=${process.env.NODE_ENV === 'production'}; samesite=strict`
        }
      } else if (event === 'SIGNED_OUT') {
        console.info('User signed out')
        logAuthEvent('signed_out', 'unknown')
        
        // Reset profile state on logout
        dispatch(resetProfileState())
        
        // Clear session timestamp
        document.cookie = 'session-timestamp=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
    if (!isAuthEnabled) {
      return { error: new Error('Authentication is disabled') }
    }
    
    const supabase = createClient()
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signUp = async (email: string, password: string) => {
    const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
    if (!isAuthEnabled) {
      return { error: new Error('Authentication is disabled') }
    }
    
    const supabase = createClient()
    const { error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { error }
  }

  const signOut = async () => {
    try {
      const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
      if (!isAuthEnabled) {
        return { error: new Error('Authentication is disabled') }
      }
      
      // Log the signout attempt
      if (user?.email) {
        logAuthEvent('signout_initiated', user.email)
      }

      // Call API logout endpoint for comprehensive cleanup
      try {
        const response = await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session?.access_token}`,
          },
        })

        if (!response.ok) {
          console.warn('API logout failed, proceeding with client-side cleanup')
        } else {
          const result = await response.json()
          console.info('API logout successful:', result.message)
        }
      } catch (apiError) {
        console.warn('API logout request failed:', apiError)
        // Continue with client-side cleanup even if API fails
      }

      // Sign out from Supabase
      const supabase = createClient()
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        logAuthEvent('signout_error', user?.email || 'unknown')
        return { error }
      }

      // Clear all auth-related data from browser storage
      if (typeof window !== 'undefined') {
        // Clear localStorage auth data
        localStorage.removeItem('sb-auth')
        localStorage.removeItem('sb-auth-token')
        
        // Clear sessionStorage
        sessionStorage.removeItem('auth_events')
        
        // Clear all auth-related cookies
        document.cookie = 'sb-access-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        document.cookie = 'sb-refresh-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        document.cookie = 'session-timestamp=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        document.cookie = 'sb-auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      }

      logAuthEvent('signout_completed', 'user_signed_out')
      return { error: null }
    } catch (err) {
      const error = err as Error
      logAuthEvent('signout_exception', user?.email || 'unknown')
      return { error }
    }
  }

  const resetPassword = async (email: string) => {
    const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
    if (!isAuthEnabled) {
      return { error: new Error('Authentication is disabled') }
    }
    
    const supabase = createClient()
    const { error } = await supabase.auth.resetPasswordForEmail(email)
    return { error }
  }

  const value = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}