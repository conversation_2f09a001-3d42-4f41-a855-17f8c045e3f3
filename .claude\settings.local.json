{"permissions": {"allow": ["<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(task-master:*)", "Bash(find:*)", "mcp__taskmaster-ai__initialize_project", "mcp__taskmaster-ai__models", "mcp__taskmaster-ai__parse_prd", "mcp__taskmaster-ai__next_task", "mcp__taskmaster-ai__get_tasks", "mcp__taskmaster-ai__set_task_status", "mcp__taskmaster-ai__expand_task", "mcp__taskmaster-ai__update_subtask", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "mcp__taskmaster-ai__get_task", "<PERSON><PERSON>(docker-compose up:*)", "mcp__supabase__list_tables", "mcp__taskmaster-ai__update_task", "Bash(python -m pytest tests/ -k \"test_auth\" -v)", "mcp__supabase__execute_sql", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm install:*)", "Bash(node:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker-compose restart:*)", "Bash(rg:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose down:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(sed:*)", "mcp__taskmaster-ai__add_task", "mcp__taskmaster-ai__remove_dependency", "Bash(npm run:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(docker-compose build:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_install", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_take_screenshot", "Bash(docker logs:*)", "<PERSON><PERSON>(docker-compose stop:*)", "<PERSON><PERSON>(docker-compose rm:*)", "Bash(docker logs:*)", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_wait_for", "WebFetch(domain:ui.shadcn.com)", "WebFetch(domain:supabase.com)", "<PERSON><PERSON>(mv:*)", "mcp__playwright__browser_close", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__browserbase__browserbase_session_create", "mcp__browserbase__browserbase_navigate", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker:*)", "mcp__browserbase__browserbase_session_close", "Bash(npx:*)", "<PERSON><PERSON>(diff:*)", "mcp__playwright__browser_press_key", "mcp__playwright__browser_snapshot", "Bash(export:*)", "mcp__sequential-thinking__sequentialthinking"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}