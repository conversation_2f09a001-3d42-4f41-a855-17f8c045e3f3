#!/bin/bash
# verify-backup.sh - <PERSON>ript to verify the latest MinIO backup

set -e

LOG_FILE="/home/<USER>/memory-master-v2/logs/backup.log"
MINIO_ALIAS="remoteminio"
BUCKET_NAME="memory-master-dev-backups"
VERIFICATION_DIR="/tmp/backup_verification_$(date +%Y%m%d_%H%M%S)"
TELEGRAM_WEBHOOK_URL="https://workflow.syncrobit.net/webhook/v1/send-telegram-notification"

send_telegram_notification() {
    local message=$1
    curl -s -X POST -H "Content-Type: text/plain" -d "$message" $TELEGRAM_WEBHOOK_URL
}

run_verification() {
    echo "$(date): Starting backup verification..." | tee -a $LOG_FILE

    # Create a temporary directory for verification
    mkdir -p $VERIFICATION_DIR
    cd $VERIFICATION_DIR

    echo "Downloading latest backup from MinIO..." | tee -a $LOG_FILE
    if ! docker exec memory-backup-storage mc cp $MINIO_ALIAS/$BUCKET_NAME/latest-backup.tar.gz /tmp/; then
        echo "$(date): ❌ Verification failed: Could not download latest backup." | tee -a $LOG_FILE
        return 1
    fi

    echo "Copying backup from container to host..." | tee -a $LOG_FILE
    if ! docker cp memory-backup-storage:/tmp/latest-backup.tar.gz .; then
        echo "$(date): ❌ Verification failed: Could not copy backup from container." | tee -a $LOG_FILE
        return 1
    fi

    echo "Extracting backup..." | tee -a $LOG_FILE
    tar -xzf latest-backup.tar.gz

    # Clean up the temporary file in container
    docker exec memory-backup-storage rm -f /tmp/latest-backup.tar.gz

    # Find the backup directory
    BACKUP_DIR=$(find . -type d -name "backup_*" -print -quit)
    if [ -z "$BACKUP_DIR" ]; then
        echo "$(date): ❌ Verification failed: Could not find backup directory in the archive." | tee -a $LOG_FILE
        return 1
    fi
    cd $BACKUP_DIR

    echo "Performing verification checks..." | tee -a $LOG_FILE

    # 1. Check for manifest file
    if [ ! -f "backup_manifest.json" ]; then
        echo "$(date): ❌ Verification failed: backup_manifest.json not found." | tee -a $LOG_FILE
        return 1
    fi
    echo "✅ Manifest file found." | tee -a $LOG_FILE

    # 2. Check for SQLite database file
    if [ ! -f "sqlite/openmemory.db" ]; then
        echo "$(date): ❌ Verification failed: sqlite/openmemory.db not found." | tee -a $LOG_FILE
        return 1
    fi
    echo "✅ SQLite backup file found." | tee -a $LOG_FILE

    # 3. Check for Qdrant data
    if [ ! -f "qdrant/qdrant_data.tar.gz" ]; then
        echo "$(date): ❌ Verification failed: qdrant/qdrant_data.tar.gz not found." | tee -a $LOG_FILE
        return 1
    fi
    echo "✅ Qdrant backup file found." | tee -a $LOG_FILE

    # 4. Check for config files
    if [ ! -f "config/docker-compose.yml" ]; then
        echo "$(date): ❌ Verification failed: config/docker-compose.yml not found." | tee -a $LOG_FILE
        return 1
    fi
    echo "✅ Docker-compose backup file found." | tee -a $LOG_FILE

    echo "$(date): ✅ Backup verification successful." | tee -a $LOG_FILE
    return 0
}

if run_verification; then
    send_telegram_notification "✅ Memory-Master backup verification successful."
else
    send_telegram_notification "❌ Memory-Master backup verification FAILED."
fi

# Clean up
cd /tmp
rm -rf $VERIFICATION_DIR

exit 0