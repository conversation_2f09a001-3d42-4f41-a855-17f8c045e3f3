# Environment Configuration Guide

## Environment Files

This project uses **`.env`** as the primary environment configuration file. 

### File Structure
- **`.env`** - Primary environment file (used by Docker containers and development)
- **`.env.example`** - Template with example values for setup

### Next.js Environment Loading Order
Next.js loads environment variables in this precedence order:
1. `.env.local` (highest priority - **removed from this project**)
2. `.env.development`/`.env.production`/`.env.test`
3. **`.env`** (current primary file)
4. `.env.example` (lowest priority)

### Configuration Variables

#### Required Variables
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8765

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=http://*************:8000
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Authentication Feature Flag
NEXT_PUBLIC_AUTH_ENABLED=true
```

#### Removed Variables (Per Auth PRD)
- `NEXT_PUBLIC_USER_ID` - **Removed** as authentication is now fully implemented
- `USER` - **Removed** as system configuration is no longer needed

### Authentication Implementation

The authentication system is fully implemented per the PRD:

1. **User Authentication**: Users log in with Supabase credentials
2. **Dynamic User ID**: Profile state is updated with authenticated user's email
3. **No Hardcoded Users**: All user-specific data is now dynamically assigned

### Integration Points

#### Profile State Management
- `profileSlice.userId` is set to authenticated user's email
- State is automatically updated on login/logout
- No environment variable fallbacks

#### Component Integration
- `AuthProvider` dispatches `setUserId()` on successful authentication
- `Navbar` shows authenticated user information
- All API calls use authenticated user context

### Development Setup

1. Copy `.env.example` to `.env`
2. Update Supabase credentials
3. Ensure `NEXT_PUBLIC_AUTH_ENABLED=true`
4. Start Docker containers

### Docker Integration

The `.env` file is automatically loaded by:
- Docker Compose services
- Next.js development server
- Production builds

### Security Notes

- Environment variables prefixed with `NEXT_PUBLIC_` are exposed to the browser
- Supabase keys are safe to expose (anon key has row-level security)
- Never commit actual credentials to version control