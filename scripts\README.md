# Memory Master User Management Scripts

This directory contains administrative scripts for managing users in the Memory Master system.

## Quick Start

1. **Set up environment variables**:
   ```bash
   export SUPABASE_URL="https://your-project.supabase.co"
   export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
   ```

2. **Install dependencies**:
   ```bash
   pip install -r scripts/requirements.txt
   ```

3. **Create project users**:
   ```bash
   python scripts/create_users.py
   ```

## Scripts Overview

### 🚀 create_users.py
Creates <PERSON><PERSON> and <PERSON><PERSON>'s accounts with secure passwords and proper metadata.

**Usage:**
```bash
python scripts/create_users.py
```

**Features:**
- ✅ Automatic secure password generation
- ✅ Duplicate user detection
- ✅ Comprehensive error handling
- ✅ Detailed progress logging

### 🔐 reset_password.py
Manages user passwords and provides user listing functionality.

**Usage:**
```bash
# List all users
python scripts/reset_password.py --list-users

# Reset password (auto-generated)
python scripts/reset_password.py --email <EMAIL>

# Reset with specific password
python scripts/reset_password.py --email <EMAIL> --password "NewPassword123!"

# Get reset instructions
python scripts/reset_password.py --generate-reset-link <EMAIL>
```

### 💾 backup_users.py
Handles user data backup and restore operations.

**Usage:**
```bash
# Backup all users
python scripts/backup_users.py --backup --output backup.json

# Backup specific user
python scripts/backup_users.py --backup --email <EMAIL> --output user.json

# List backup contents
python scripts/backup_users.py --list-backup backup.json

# Restore (dry run)
python scripts/backup_users.py --restore backup.json --dry-run

# Restore (actual)
python scripts/backup_users.py --restore backup.json
```

## Documentation

- **[USER_MANAGEMENT_GUIDE.md](USER_MANAGEMENT_GUIDE.md)**: Comprehensive user management documentation
- **[requirements.txt](requirements.txt)**: Python dependencies

## Security Notes

⚠️ **Important Security Considerations:**

1. **Service Role Key**: Never commit service role keys to version control
2. **Password Security**: Generated passwords should be shared securely
3. **Backup Security**: Store user backups in secure locations
4. **Audit Trail**: All operations are logged for security auditing

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SUPABASE_URL` | Your Supabase project URL | ✅ Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Service role key (not anon key) | ✅ Yes |

## Error Handling

All scripts include comprehensive error handling:

- ✅ Missing environment variables
- ✅ Network connectivity issues
- ✅ Invalid credentials
- ✅ Duplicate users
- ✅ Permission errors
- ✅ File I/O errors

## Best Practices

1. **Test First**: Use dry-run options when available
2. **Backup Before Changes**: Always backup before major operations
3. **Verify Results**: Check Supabase dashboard after operations
4. **Secure Passwords**: Use generated passwords or enforce strong policies
5. **Monitor Logs**: Review script outputs for any issues

## Troubleshooting

### Common Issues

**Environment variables not set:**
```bash
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY
```

**Permission denied:**
- Verify service role key is correct
- Check Supabase project settings
- Ensure auth API is enabled

**User already exists:**
- Use `--list-users` to check existing users
- Scripts automatically skip duplicates

**Import errors:**
```bash
pip install -r scripts/requirements.txt
```

## Contributing

When adding new scripts:

1. Follow the existing code structure
2. Include comprehensive error handling
3. Add logging for all operations
4. Update this README
5. Add usage examples

---

*For detailed documentation, see [USER_MANAGEMENT_GUIDE.md](USER_MANAGEMENT_GUIDE.md)*