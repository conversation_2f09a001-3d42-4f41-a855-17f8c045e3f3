# User Management Guide for Memory Master

This guide provides comprehensive instructions for managing users in the Memory Master authentication system.

## Overview

Memory Master uses Supabase Auth for user authentication. This document covers:
- Automated user creation scripts
- Manual user creation via Supabase Dashboard
- Password management
- User data backup and restore
- Security best practices

## Prerequisites

### Environment Variables

Ensure these environment variables are set:

```bash
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

### Dependencies

Install required Python packages:

```bash
pip install supabase
```

## Automated User Creation

### Creating Project Users

Use the automated script to create <PERSON><PERSON> and <PERSON><PERSON>'s accounts:

```bash
cd /path/to/memory-master-v2
python scripts/create_users.py
```

This script will:
- ✅ Create users with secure passwords
- ✅ Set up proper metadata
- ✅ Skip existing users
- ✅ Provide detailed logging
- ✅ Generate summary report

### Script Features

- **Email Validation**: Checks for existing users before creation
- **Secure Passwords**: Generates strong passwords automatically
- **Metadata**: Sets up user roles and project information
- **Error Handling**: Comprehensive error reporting
- **Logging**: Detailed operation logs

## Manual User Creation via Supabase Dashboard

### Step-by-Step Process

1. **Access Supabase Dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Navigate to your Memory Master project
   - Click on "Authentication" in the sidebar

2. **Create New User**
   - Click "Add User" button
   - Fill in user details:
     - **Email**: User's email address
     - **Password**: Secure password (min 8 characters)
     - **Email Confirmation**: Check "Confirm email" if you want to skip verification
   - Add metadata if needed:
     ```json
     {
       "full_name": "User Full Name",
       "role": "admin",
       "project": "memory-master"
     }
     ```

3. **Verify User Creation**
   - User should appear in the users list
   - Note the User ID for reference
   - Check email confirmation status

### User Roles and Metadata

Add this metadata for project users:

```json
{
  "full_name": "User's Full Name",
  "role": "admin",
  "created_by": "admin",
  "project": "memory-master",
  "created_at": "2024-01-01T00:00:00.000Z"
}
```

## Password Management

### Automated Password Reset

Reset a user's password using the script:

```bash
# Generate new password automatically
python scripts/reset_password.py --email <EMAIL>

# Set specific password
python scripts/reset_password.py --email <EMAIL> --password "NewSecurePassword123!"

# List all users
python scripts/reset_password.py --list-users

# Get password reset instructions
python scripts/reset_password.py --generate-reset-link <EMAIL>
```

### Manual Password Reset

1. **Via Supabase Dashboard**:
   - Go to Authentication > Users
   - Find the user
   - Click the user row
   - Click "Send password reset email"

2. **Via Client SDK** (for frontend integration):
   ```typescript
   await supabase.auth.resetPasswordForEmail('<EMAIL>')
   ```

### Password Security Requirements

- **Minimum Length**: 8 characters
- **Complexity**: Mix of uppercase, lowercase, numbers, and special characters
- **No Common Passwords**: Avoid dictionary words or personal information
- **Regular Updates**: Encourage users to change passwords periodically

## User Data Backup and Restore

### Creating Backups

```bash
# Backup all users
python scripts/backup_users.py --backup --output all_users_backup.json

# Backup specific user
python scripts/backup_users.py --backup --email <EMAIL> --output user_backup.json

# List backup contents
python scripts/backup_users.py --list-backup all_users_backup.json
```

### Restoring from Backup

```bash
# Dry run (preview changes)
python scripts/backup_users.py --restore all_users_backup.json --dry-run

# Actual restore
python scripts/backup_users.py --restore all_users_backup.json
```

### What Gets Backed Up

- ✅ User ID
- ✅ Email address
- ✅ Email confirmation status
- ✅ Creation and update timestamps
- ✅ Last sign-in time
- ✅ User metadata
- ✅ App metadata
- ❌ Passwords (cannot be extracted for security)

### Restore Limitations

- **New User IDs**: Restored users get new UUIDs
- **Password Reset Required**: Users must reset passwords after restore
- **Metadata Preserved**: All custom metadata is maintained
- **Duplicate Prevention**: Existing users are skipped

## Security Best Practices

### Password Policies

1. **Enforce Strong Passwords**:
   - Minimum 12 characters
   - Mix of character types
   - No personal information
   - No dictionary words

2. **Enable MFA** (when available):
   - Set up in Supabase Dashboard
   - Require for admin users
   - Use authenticator apps

3. **Monitor Access**:
   - Review auth logs regularly
   - Check for suspicious activity
   - Monitor failed login attempts

### User Account Security

1. **Regular Audits**:
   - Review user list monthly
   - Remove inactive accounts
   - Verify user permissions

2. **Access Control**:
   - Implement role-based access
   - Regular permission reviews
   - Principle of least privilege

3. **Data Protection**:
   - Encrypt sensitive data
   - Secure backup storage
   - Regular backup testing

## Troubleshooting

### Common Issues

#### "User already exists" Error
```bash
# Check existing users
python scripts/reset_password.py --list-users

# Use different email or skip creation
```

#### Environment Variables Not Set
```bash
# Check current values
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY

# Set missing variables
export SUPABASE_URL="your-url"
export SUPABASE_SERVICE_ROLE_KEY="your-key"
```

#### Permission Denied
- Verify service role key is correct
- Check Supabase project permissions
- Ensure API is enabled

#### Import Errors
```bash
# Install missing packages
pip install supabase

# Or install all requirements
pip install -r requirements.txt
```

### Getting Help

1. **Check Logs**: All scripts provide detailed logging
2. **Supabase Dashboard**: Check auth logs and user status
3. **Documentation**: Review Supabase Auth documentation
4. **Support**: Contact system administrators

## Maintenance Tasks

### Regular Maintenance

#### Weekly
- [ ] Review auth logs
- [ ] Check for failed login attempts
- [ ] Verify user account status

#### Monthly
- [ ] Backup user data
- [ ] Review user permissions
- [ ] Update passwords if needed
- [ ] Clean up inactive accounts

#### Quarterly
- [ ] Full security audit
- [ ] Test backup/restore process
- [ ] Review and update procedures
- [ ] Update documentation

### Monitoring

Set up monitoring for:
- Failed authentication attempts
- New user registrations
- Password reset requests
- Unusual access patterns

## Project-Specific Users

### Current Users

| Email | Role | Purpose |
|-------|------|---------|
| <EMAIL> | admin | Primary administrator |
| <EMAIL> | admin | Secondary administrator |

### Adding New Users

When adding new users:
1. Use appropriate email domain
2. Set proper metadata
3. Assign correct permissions
4. Document the addition
5. Notify the user securely

---

## Script Reference

### create_users.py
Creates project-specific users with secure passwords and metadata.

### reset_password.py
Manages password resets and user listing.

### backup_users.py
Handles user data backup and restore operations.

---

*Last updated: 2024-07-03*
*Version: 1.0*