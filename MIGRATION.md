# Memory Master Authentication Migration Guide

## Overview

This document outlines the migration strategy from hardcoded user authentication to Supabase-based authentication system with zero downtime.

## Pre-Migration Checklist

### 1. Environment Validation
- [ ] Verify all required API keys are configured
- [ ] Confirm Supabase project is properly set up
- [ ] Test authentication flow in staging environment
- [ ] Validate both UI and API feature flags are working

### 2. Data Backup
- [ ] Export all existing memory data
- [ ] Backup Qdrant vector store
- [ ] Export user-specific configurations
- [ ] Document current user mappings

### 3. User Account Setup
- [ ] Create Supabase users for existing hardcoded accounts
- [ ] Map existing usernames to email addresses
- [ ] Test login flow for each migrated user
- [ ] Verify data access permissions

## Migration Steps

### Phase 1: Preparation (Pre-Deployment)

#### 1.1 Staging Environment Testing
```bash
# Deploy to staging with auth enabled
export NEXT_PUBLIC_AUTH_ENABLED=true
export AUTH_ENABLED=true

# Test complete user flow
npm run test:e2e
```

#### 1.2 User Account Creation
```sql
-- Create users in Supabase auth.users table
-- Example mapping:
-- hardcoded_user_1 -> <EMAIL>
-- hardcoded_user_2 -> <EMAIL>
```

#### 1.3 Data Migration Scripts
```bash
# Run data migration to associate existing memories with new user IDs
python scripts/migrate_user_data.py
```

### Phase 2: Deployment (Zero Downtime)

#### 2.1 Deploy API with Backward Compatibility
```bash
# Deploy API with auth middleware but feature flag disabled
export AUTH_ENABLED=false
docker-compose up -d api

# Verify API is running and backward compatible
curl -f http://localhost:8765/health
```

#### 2.2 Deploy Frontend with Feature Flag
```bash
# Deploy UI with auth code but feature flag disabled
export NEXT_PUBLIC_AUTH_ENABLED=false
docker-compose up -d ui

# Verify UI is accessible
curl -f http://localhost:3000/health
```

#### 2.3 Enable Authentication (Gradual Rollout)
```bash
# Enable auth on API first
docker-compose exec api sh -c "export AUTH_ENABLED=true && supervisorctl restart api"

# Wait 5 minutes and monitor logs
docker-compose logs -f api

# Enable auth on UI if no issues
docker-compose exec ui sh -c "export NEXT_PUBLIC_AUTH_ENABLED=true && npm run build && pm2 restart all"
```

#### 2.4 Monitor and Validate
```bash
# Monitor error rates
docker-compose logs -f --tail=100

# Test user login flow
curl -X POST http://localhost:8765/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"secure_password"}'

# Verify data access
curl -H "Authorization: Bearer $ACCESS_TOKEN" \
  http://localhost:8765/memories
```

### Phase 3: Cleanup (Post-Migration)

#### 3.1 Remove Legacy Code (Wait 24-48 hours)
- [ ] Remove `NEXT_PUBLIC_USER_ID` from environment variables
- [ ] Remove `USER` environment variable from API
- [ ] Clean up hardcoded user references in code
- [ ] Remove feature flags after stable operation

#### 3.2 Update Documentation
- [ ] Update deployment instructions
- [ ] Update API documentation
- [ ] Update user onboarding guides
- [ ] Update troubleshooting documentation

## Rollback Plan

### Emergency Rollback (if issues occur)

#### Option 1: Feature Flag Rollback (Fastest)
```bash
# Disable auth via feature flags
export NEXT_PUBLIC_AUTH_ENABLED=false
export AUTH_ENABLED=false

# Restart services
docker-compose restart
```

#### Option 2: Full Deployment Rollback
```bash
# Restore previous deployment
git checkout $PREVIOUS_COMMIT
docker-compose down
docker-compose up -d

# Restore environment variables
cp .env.backup .env
```

#### Option 3: Database Rollback (Last Resort)
```bash
# Restore from backup
psql $DATABASE_URL < backup_pre_migration.sql

# Restore Qdrant data
docker-compose exec qdrant sh -c "rm -rf /qdrant/storage/* && tar -xzf /backup/qdrant_backup.tar.gz -C /qdrant/storage/"
```

## Environment Variable Changes

### Before Migration
```bash
# UI (.env)
NEXT_PUBLIC_API_URL=http://localhost:8765
NEXT_PUBLIC_USER_ID=hardcoded_user
NEXT_PUBLIC_AUTH_ENABLED=false

# API (.env)
USER=hardcoded_user
AUTH_ENABLED=false
```

### After Migration
```bash
# UI (.env)
NEXT_PUBLIC_API_URL=http://localhost:8765
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...
NEXT_PUBLIC_AUTH_ENABLED=true
# NEXT_PUBLIC_USER_ID=removed

# API (.env)
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...
SUPABASE_JWT_SECRET=your-jwt-secret
SUPABASE_DATABASE_URL=postgresql://...
AUTH_ENABLED=true
# USER=removed
```

## Monitoring and Validation

### Key Metrics to Monitor
- [ ] User login success rate
- [ ] API error rates
- [ ] Memory operation success rates
- [ ] Session timeout functionality
- [ ] Rate limiting effectiveness

### Success Criteria
- [ ] All existing users can log in successfully
- [ ] All memories are accessible with correct ownership
- [ ] No data loss during migration
- [ ] Performance remains stable
- [ ] Security features are functioning

### Post-Migration Testing Checklist
- [ ] User authentication flow works
- [ ] Memory CRUD operations work
- [ ] App creation and management works
- [ ] Evolution features work
- [ ] MCP configuration displays correctly
- [ ] Session management works properly
- [ ] Rate limiting is effective
- [ ] Security headers are present

## Troubleshooting

### Common Issues and Solutions

#### Authentication Failures
- Verify Supabase configuration
- Check JWT secret configuration
- Validate user exists in auth.users table

#### Data Access Issues
- Verify user ID mapping in database
- Check row-level security policies
- Validate session token is valid

#### Performance Issues
- Monitor database connection pool
- Check for N+1 query problems
- Validate caching is working

#### Session Problems
- Verify cookie configuration
- Check session timeout settings
- Validate CSRF protection

## Contact Information

- **Technical Lead**: [Name] - [email]
- **DevOps Team**: [Name] - [email]
- **Database Admin**: [Name] - [email]

## Migration Timeline

| Phase | Duration | Activities |
|-------|----------|------------|
| Preparation | 2-3 days | Testing, user creation, data backup |
| Deployment | 2-4 hours | Rolling deployment with monitoring |
| Monitoring | 24-48 hours | Error monitoring and validation |
| Cleanup | 1-2 days | Remove legacy code and update docs |

---

**Last Updated**: 2025-07-03  
**Document Version**: 1.0  
**Migration Status**: Ready for Execution