#!/bin/bash

# Memory Master Authentication Migration Deployment Script
# This script automates the zero-downtime migration to authenticated system

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$PROJECT_ROOT/logs/migration_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${2:-$NC}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    log "ERROR: $1" "$RED"
    exit 1
}

warning() {
    log "WARNING: $1" "$YELLOW"
}

info() {
    log "INFO: $1" "$BLUE"
}

success() {
    log "SUCCESS: $1" "$GREEN"
}

# Help function
show_help() {
    cat << EOF
Memory Master Authentication Migration Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    --dry-run           Run in dry-run mode (no actual changes)
    --skip-backup       Skip backup creation (not recommended)
    --skip-validation   Skip pre-deployment validation
    --rollback          Rollback to previous state
    --stage STAGE       Deployment stage (prepare|deploy|validate|cleanup)
    --help              Show this help message

STAGES:
    prepare     - Create backups and validate environment
    deploy      - Deploy with feature flags disabled
    enable      - Enable authentication features
    validate    - Validate migration success
    cleanup     - Remove legacy code and configurations

ENVIRONMENT VARIABLES:
    MIGRATION_MODE      - Set to 'production' for production deployment
    BACKUP_RETENTION    - Number of backups to keep (default: 5)

Examples:
    $0 --dry-run                    # Test the deployment
    $0 --stage prepare             # Only run preparation
    $0 --stage deploy              # Deploy with auth disabled
    $0 --stage enable              # Enable authentication
    $0 --rollback                  # Rollback deployment

EOF
}

# Parse command line arguments
DRY_RUN=false
SKIP_BACKUP=false
SKIP_VALIDATION=false
ROLLBACK=false
STAGE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-validation)
            SKIP_VALIDATION=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --stage)
            STAGE="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            ;;
    esac
done

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Start logging
info "Starting Memory Master Authentication Migration"
info "Timestamp: $TIMESTAMP"
info "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "LIVE DEPLOYMENT")"

# Validate environment
validate_environment() {
    info "Validating environment..."
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        error "docker-compose.yml not found. Are you in the right directory?"
    fi
    
    # Check required environment files
    if [[ ! -f "$PROJECT_ROOT/ui/.env" ]]; then
        error "UI .env file not found"
    fi
    
    if [[ ! -f "$PROJECT_ROOT/api/.env" ]]; then
        error "API .env file not found"
    fi
    
    # Check if services are running
    if ! docker-compose ps | grep -q "Up"; then
        warning "Some services are not running. Starting services..."
        docker-compose up -d
        sleep 10
    fi
    
    # Test API health
    if ! curl -s -f http://localhost:8765/health > /dev/null; then
        error "API health check failed"
    fi
    
    # Test UI health
    if ! curl -s -f http://localhost:3000 > /dev/null; then
        error "UI health check failed"
    fi
    
    success "Environment validation completed"
}

# Create backup
create_backup() {
    if [[ "$SKIP_BACKUP" = true ]]; then
        warning "Skipping backup creation"
        return
    fi
    
    info "Creating backup..."
    
    local backup_dir="$PROJECT_ROOT/backups/migration_${TIMESTAMP}"
    mkdir -p "$backup_dir"
    
    # Backup environment files
    cp "$PROJECT_ROOT/ui/.env" "$backup_dir/ui_env_backup"
    cp "$PROJECT_ROOT/api/.env" "$backup_dir/api_env_backup"
    
    # Backup docker-compose configuration
    cp "$PROJECT_ROOT/docker-compose.yml" "$backup_dir/docker_compose_backup.yml"
    
    # Create database backup
    if [[ -n "$SUPABASE_DATABASE_URL" ]]; then
        info "Creating database backup..."
        if [[ "$DRY_RUN" = false ]]; then
            pg_dump "$SUPABASE_DATABASE_URL" > "$backup_dir/database_backup.sql" || warning "Database backup failed"
        else
            info "[DRY RUN] Would create database backup"
        fi
    else
        warning "SUPABASE_DATABASE_URL not set, skipping database backup"
    fi
    
    # Backup Qdrant data
    info "Creating Qdrant backup..."
    if [[ "$DRY_RUN" = false ]]; then
        docker-compose exec -T qdrant tar -czf /tmp/qdrant_backup_${TIMESTAMP}.tar.gz -C /qdrant/storage . || warning "Qdrant backup failed"
        docker cp "$(docker-compose ps -q qdrant):/tmp/qdrant_backup_${TIMESTAMP}.tar.gz" "$backup_dir/" || warning "Qdrant backup copy failed"
    else
        info "[DRY RUN] Would create Qdrant backup"
    fi
    
    success "Backup created at $backup_dir"
}

# Deploy with feature flags disabled
deploy_services() {
    info "Deploying services with authentication disabled..."
    
    # Update API environment to include auth but keep it disabled
    local api_env="$PROJECT_ROOT/api/.env"
    if [[ "$DRY_RUN" = false ]]; then
        # Ensure AUTH_ENABLED is false
        if grep -q "AUTH_ENABLED=" "$api_env"; then
            sed -i 's/AUTH_ENABLED=.*/AUTH_ENABLED=false/' "$api_env"
        else
            echo "AUTH_ENABLED=false" >> "$api_env"
        fi
        
        # Restart API
        docker-compose up -d --force-recreate api
        sleep 10
        
        # Verify API is healthy
        if ! curl -s -f http://localhost:8765/health > /dev/null; then
            error "API deployment failed - health check failed"
        fi
    else
        info "[DRY RUN] Would update API configuration and restart"
    fi
    
    # Update UI environment
    local ui_env="$PROJECT_ROOT/ui/.env"
    if [[ "$DRY_RUN" = false ]]; then
        # Ensure NEXT_PUBLIC_AUTH_ENABLED is false
        if grep -q "NEXT_PUBLIC_AUTH_ENABLED=" "$ui_env"; then
            sed -i 's/NEXT_PUBLIC_AUTH_ENABLED=.*/NEXT_PUBLIC_AUTH_ENABLED=false/' "$ui_env"
        else
            echo "NEXT_PUBLIC_AUTH_ENABLED=false" >> "$ui_env"
        fi
        
        # Restart UI
        docker-compose up -d --force-recreate ui
        sleep 15
        
        # Verify UI is healthy
        if ! curl -s -f http://localhost:3000 > /dev/null; then
            error "UI deployment failed - health check failed"
        fi
    else
        info "[DRY RUN] Would update UI configuration and restart"
    fi
    
    success "Services deployed successfully"
}

# Run data migration
run_data_migration() {
    info "Running data migration..."
    
    local migration_script="$PROJECT_ROOT/scripts/migrate_user_data.py"
    
    if [[ ! -f "$migration_script" ]]; then
        error "Migration script not found: $migration_script"
    fi
    
    # Run migration script
    local migration_args=""
    if [[ "$DRY_RUN" = true ]]; then
        migration_args="--dry-run"
    fi
    
    cd "$PROJECT_ROOT"
    python3 "$migration_script" $migration_args --verbose
    
    success "Data migration completed"
}

# Enable authentication
enable_authentication() {
    info "Enabling authentication features..."
    
    # Enable on API first
    local api_env="$PROJECT_ROOT/api/.env"
    if [[ "$DRY_RUN" = false ]]; then
        sed -i 's/AUTH_ENABLED=.*/AUTH_ENABLED=true/' "$api_env"
        docker-compose exec api supervisorctl restart all || docker-compose restart api
        sleep 10
        
        # Verify API still healthy
        if ! curl -s -f http://localhost:8765/health > /dev/null; then
            error "API failed after enabling authentication"
        fi
        
        info "API authentication enabled"
        
        # Wait a moment and check logs
        sleep 5
        if docker-compose logs --tail=20 api | grep -i error; then
            warning "Errors detected in API logs"
        fi
    else
        info "[DRY RUN] Would enable API authentication"
    fi
    
    # Enable on UI
    local ui_env="$PROJECT_ROOT/ui/.env"
    if [[ "$DRY_RUN" = false ]]; then
        sed -i 's/NEXT_PUBLIC_AUTH_ENABLED=.*/NEXT_PUBLIC_AUTH_ENABLED=true/' "$ui_env"
        docker-compose restart ui
        sleep 15
        
        # Verify UI still healthy
        if ! curl -s -f http://localhost:3000 > /dev/null; then
            error "UI failed after enabling authentication"
        fi
        
        info "UI authentication enabled"
        
        # Check UI logs
        sleep 5
        if docker-compose logs --tail=20 ui | grep -i error; then
            warning "Errors detected in UI logs"
        fi
    else
        info "[DRY RUN] Would enable UI authentication"
    fi
    
    success "Authentication enabled successfully"
}

# Validate migration
validate_migration() {
    info "Validating migration..."
    
    # Test authentication endpoint
    if [[ "$DRY_RUN" = false ]]; then
        # This would require actual test users and passwords
        info "Testing authentication endpoints..."
        # curl -X POST http://localhost:8765/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"test"}'
    else
        info "[DRY RUN] Would test authentication endpoints"
    fi
    
    # Check error rates
    info "Checking error rates..."
    local error_count=$(docker-compose logs --tail=100 api | grep -i error | wc -l)
    if [[ $error_count -gt 5 ]]; then
        warning "High error count detected: $error_count errors in last 100 log lines"
    fi
    
    success "Migration validation completed"
}

# Cleanup legacy code
cleanup_legacy() {
    info "Cleaning up legacy code..."
    
    if [[ "$DRY_RUN" = false ]]; then
        # Remove legacy environment variables
        local api_env="$PROJECT_ROOT/api/.env"
        local ui_env="$PROJECT_ROOT/ui/.env"
        
        # Remove from API
        sed -i '/^USER=/d' "$api_env"
        
        # Remove from UI  
        sed -i '/^NEXT_PUBLIC_USER_ID=/d' "$ui_env"
        
        info "Legacy environment variables removed"
    else
        info "[DRY RUN] Would remove legacy environment variables"
    fi
    
    success "Legacy cleanup completed"
}

# Rollback function
rollback_deployment() {
    info "Rolling back deployment..."
    
    local latest_backup=$(ls -t "$PROJECT_ROOT/backups/" | head -n1)
    if [[ -z "$latest_backup" ]]; then
        error "No backup found for rollback"
    fi
    
    local backup_dir="$PROJECT_ROOT/backups/$latest_backup"
    
    if [[ "$DRY_RUN" = false ]]; then
        # Restore environment files
        cp "$backup_dir/ui_env_backup" "$PROJECT_ROOT/ui/.env"
        cp "$backup_dir/api_env_backup" "$PROJECT_ROOT/api/.env"
        
        # Restore services
        docker-compose down
        docker-compose up -d
        sleep 15
        
        # Verify services
        if ! curl -s -f http://localhost:8765/health > /dev/null; then
            error "API health check failed after rollback"
        fi
        
        if ! curl -s -f http://localhost:3000 > /dev/null; then
            error "UI health check failed after rollback"
        fi
    else
        info "[DRY RUN] Would restore from backup: $backup_dir"
    fi
    
    success "Rollback completed successfully"
}

# Main execution logic
main() {
    if [[ "$ROLLBACK" = true ]]; then
        rollback_deployment
        return
    fi
    
    # Execute based on stage or run all stages
    case "$STAGE" in
        "prepare")
            validate_environment
            create_backup
            ;;
        "deploy")
            deploy_services
            ;;
        "migrate")
            run_data_migration
            ;;
        "enable")
            enable_authentication
            ;;
        "validate")
            validate_migration
            ;;
        "cleanup")
            cleanup_legacy
            ;;
        "")
            # Run all stages
            validate_environment
            create_backup
            deploy_services
            run_data_migration
            enable_authentication
            validate_migration
            ;;
        *)
            error "Unknown stage: $STAGE"
            ;;
    esac
    
    success "Migration deployment completed successfully!"
    info "Log file: $LOG_FILE"
}

# Trap errors and cleanup
trap 'error "Script failed at line $LINENO"' ERR

# Run main function
main "$@"