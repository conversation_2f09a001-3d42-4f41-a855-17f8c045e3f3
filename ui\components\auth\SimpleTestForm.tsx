'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function SimpleTestForm() {
  const [testValue, setTestValue] = useState('')
  const [clickCount, setClickCount] = useState(0)

  const handleClick = () => {
    console.log('Simple button clicked!')
    setClickCount(prev => prev + 1)
    alert(`But<PERSON> clicked ${clickCount + 1} times!`)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Input changed:', e.target.value)
    setTestValue(e.target.value)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted with value:', testValue)
    alert(`Form submitted with: ${testValue}`)
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Simple Test Form</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Input
              type="text"
              placeholder="Type something..."
              value={testValue}
              onChange={handleInputChange}
            />
          </div>
          
          <Button type="button" onClick={handleClick} className="w-full mb-2">
            Test Click (Count: {clickCount})
          </Button>
          
          <Button type="submit" className="w-full">
            Submit Form
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
