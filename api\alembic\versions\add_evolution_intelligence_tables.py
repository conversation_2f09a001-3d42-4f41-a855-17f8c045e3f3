"""Add evolution intelligence tables

Revision ID: e7f8a9b0c1d2
Revises: f9eeaf6a02e1
Create Date: 2025-06-29 15:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e7f8a9b0c1d2'
down_revision: Union[str, None] = 'f9eeaf6a02e1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add evolution intelligence tables."""
    
    # Create evolution_operations table
    op.create_table(
        'evolution_operations',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('app_id', sa.UUID(), nullable=False),
        sa.Column('memory_id', sa.UUID(), nullable=True),
        sa.Column('operation_type', sa.Enum('ADD', 'UPDATE', 'DELETE', 'NOOP', name='evolutionoperationtype'), nullable=False),
        sa.Column('candidate_fact', sa.Text(), nullable=False),
        sa.Column('existing_memory_content', sa.Text(), nullable=True),
        sa.Column('similarity_score', sa.Float(), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('reasoning', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['app_id'], ['memory_master.apps.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['memory_id'], ['memory_master.memories.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['user_id'], ['memory_master.users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        schema='memory_master'
    )
    
    # Create indexes for evolution_operations
    op.create_index('idx_evolution_user_date', 'evolution_operations', ['user_id', 'created_at'], unique=False, schema='memory_master')
    op.create_index('idx_evolution_app_operation', 'evolution_operations', ['app_id', 'operation_type'], unique=False, schema='memory_master')
    op.create_index('idx_evolution_memory_id', 'evolution_operations', ['memory_id'], unique=False, schema='memory_master')
    op.create_index('idx_evolution_operation_type', 'evolution_operations', ['operation_type'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_operations_app_id'), 'evolution_operations', ['app_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_operations_created_at'), 'evolution_operations', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_operations_user_id'), 'evolution_operations', ['user_id'], unique=False, schema='memory_master')
    
    # Create evolution_insights table
    op.create_table(
        'evolution_insights',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('app_id', sa.UUID(), nullable=True),
        sa.Column('date', sa.DateTime(), nullable=False),
        sa.Column('total_operations', sa.Integer(), nullable=False),
        sa.Column('add_operations', sa.Integer(), nullable=False),
        sa.Column('update_operations', sa.Integer(), nullable=False),
        sa.Column('delete_operations', sa.Integer(), nullable=False),
        sa.Column('noop_operations', sa.Integer(), nullable=False),
        sa.Column('learning_efficiency', sa.Float(), nullable=True),
        sa.Column('conflict_resolution_count', sa.Integer(), nullable=False),
        sa.Column('average_confidence', sa.Float(), nullable=True),
        sa.Column('average_similarity', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['app_id'], ['memory_master.apps.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['memory_master.users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'app_id', 'date', name='uq_evolution_insights_user_app_date'),
        schema='memory_master'
    )
    
    # Create indexes for evolution_insights
    op.create_index('idx_insights_user_date', 'evolution_insights', ['user_id', 'date'], unique=False, schema='memory_master')
    op.create_index('idx_insights_app_date', 'evolution_insights', ['app_id', 'date'], unique=False, schema='memory_master')
    op.create_index('idx_insights_date', 'evolution_insights', ['date'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_insights_app_id'), 'evolution_insights', ['app_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_insights_created_at'), 'evolution_insights', ['created_at'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_insights_date'), 'evolution_insights', ['date'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_insights_user_id'), 'evolution_insights', ['user_id'], unique=False, schema='memory_master')


def downgrade() -> None:
    """Downgrade schema to remove evolution intelligence tables."""
    
    # Drop evolution_insights table and its indexes
    op.drop_index(op.f('ix_memory_master_evolution_insights_user_id'), table_name='evolution_insights', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_insights_date'), table_name='evolution_insights', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_insights_created_at'), table_name='evolution_insights', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_insights_app_id'), table_name='evolution_insights', schema='memory_master')
    op.drop_index('idx_insights_date', table_name='evolution_insights', schema='memory_master')
    op.drop_index('idx_insights_app_date', table_name='evolution_insights', schema='memory_master')
    op.drop_index('idx_insights_user_date', table_name='evolution_insights', schema='memory_master')
    op.drop_table('evolution_insights', schema='memory_master')
    
    # Drop evolution_operations table and its indexes
    op.drop_index(op.f('ix_memory_master_evolution_operations_user_id'), table_name='evolution_operations', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_operations_created_at'), table_name='evolution_operations', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_operations_app_id'), table_name='evolution_operations', schema='memory_master')
    op.drop_index('idx_evolution_operation_type', table_name='evolution_operations', schema='memory_master')
    op.drop_index('idx_evolution_memory_id', table_name='evolution_operations', schema='memory_master')
    op.drop_index('idx_evolution_app_operation', table_name='evolution_operations', schema='memory_master')
    op.drop_index('idx_evolution_user_date', table_name='evolution_operations', schema='memory_master')
    op.drop_table('evolution_operations', schema='memory_master')
    
    # Drop the enum type
    op.execute('DROP TYPE IF EXISTS memory_master.evolutionoperationtype')