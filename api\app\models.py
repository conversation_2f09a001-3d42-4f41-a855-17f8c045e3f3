import enum
import uuid
import datetime
import enum
from sqlalchemy import (
    <PERSON>umn, String, <PERSON>ole<PERSON>, ForeignKey, Enum, Table,
    DateTime, JSON, Integer, UUID, Index, event, UniqueConstraint, Float, Text
)
from sqlalchemy.orm import relationship, Session
from app.database.base import Base
from app.utils.categorization import get_categories_for_memory


def get_current_utc_time():
    """Get current UTC time"""
    return datetime.datetime.now(datetime.UTC)


class MemoryState(enum.Enum):
    active = "active"
    paused = "paused"
    archived = "archived"
    deleted = "deleted"


class User(Base):
    __tablename__ = "users"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    user_id = Column(String, nullable=False, unique=True, index=True)
    name = Column(String, nullable=True, index=True)
    email = Column(String, unique=True, nullable=True, index=True)
    metadata_ = Column('metadata', JSON, default=dict)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    updated_at = Column(DateTime,
                        default=get_current_utc_time,
                        onupdate=get_current_utc_time)
    supabase_user_id = Column(UUID, unique=True, nullable=True)
    email_verified = Column(Boolean, default=False)
    last_sign_in_at = Column(DateTime, nullable=True)

    apps = relationship("App", back_populates="owner")
    memories = relationship("Memory", back_populates="user")

    __table_args__ = {'schema': 'memory_master'}


class App(Base):
    __tablename__ = "apps"
    __table_args__ = (
        UniqueConstraint('owner_id', 'name', name='uq_app_owner_name'),
        {'schema': 'memory_master'}
    )
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    owner_id = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    name = Column(String, nullable=False, index=True)
    description = Column(String)
    metadata_ = Column('metadata', JSON, default=dict)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    updated_at = Column(DateTime,
                        default=get_current_utc_time,
                        onupdate=get_current_utc_time)

    owner = relationship("User", back_populates="apps")
    memories = relationship("Memory", back_populates="app")


class Config(Base):
    __tablename__ = "configs"
    __table_args__ = {'schema': 'memory_master'}
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    key = Column(String, unique=True, nullable=False, index=True)
    value = Column(JSON, nullable=False)
    created_at = Column(DateTime, default=get_current_utc_time)
    updated_at = Column(DateTime,
                        default=get_current_utc_time,
                        onupdate=get_current_utc_time)


# Define the association table first
memory_categories = Table(
    "memory_categories", Base.metadata,
    Column("memory_id", UUID, ForeignKey("memory_master.memories.id"), primary_key=True, index=True),
    Column("category_id", UUID, ForeignKey("memory_master.categories.id"), primary_key=True, index=True),
    Index('idx_memory_category', 'memory_id', 'category_id'),
    schema='memory_master'
)


class Memory(Base):
    __tablename__ = "memories"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    user_id = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    app_id = Column(UUID, ForeignKey("memory_master.apps.id"), nullable=False, index=True)
    content = Column(String, nullable=False)
    vector = Column(String)
    metadata_ = Column('metadata', JSON, default=dict)
    state = Column(String, default="active", index=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    updated_at = Column(DateTime,
                        default=get_current_utc_time,
                        onupdate=get_current_utc_time)
    archived_at = Column(DateTime, nullable=True, index=True)
    deleted_at = Column(DateTime, nullable=True, index=True)

    user = relationship("User", back_populates="memories")
    app = relationship("App", back_populates="memories")
    categories = relationship("Category", secondary=memory_categories, back_populates="memories")

    __table_args__ = (
        Index('idx_memory_user_state', 'user_id', 'state'),
        Index('idx_memory_app_state', 'app_id', 'state'),
        Index('idx_memory_user_app', 'user_id', 'app_id'),
        {'schema': 'memory_master'}
    )


class Category(Base):
    __tablename__ = "categories"
    __table_args__ = {'schema': 'memory_master'}
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    name = Column(String, unique=True, nullable=False, index=True)
    description = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.UTC), index=True)
    updated_at = Column(DateTime,
                        default=get_current_utc_time,
                        onupdate=get_current_utc_time)

    memories = relationship("Memory", secondary=memory_categories, back_populates="categories")


class AccessControl(Base):
    __tablename__ = "access_controls"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    subject_type = Column(String, nullable=False, index=True)
    subject_id = Column(UUID, nullable=True, index=True)
    object_type = Column(String, nullable=False, index=True)
    object_id = Column(UUID, nullable=True, index=True)
    effect = Column(String, nullable=False, index=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)

    __table_args__ = (
        Index('idx_access_subject', 'subject_type', 'subject_id'),
        Index('idx_access_object', 'object_type', 'object_id'),
        {'schema': 'memory_master'}
    )


class ArchivePolicy(Base):
    __tablename__ = "archive_policies"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    criteria_type = Column(String, nullable=False, index=True)
    criteria_id = Column(UUID, nullable=True, index=True)
    days_to_archive = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)

    __table_args__ = (
        Index('idx_policy_criteria', 'criteria_type', 'criteria_id'),
        {'schema': 'memory_master'}
    )


class MemoryStatusHistory(Base):
    __tablename__ = "memory_status_history"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    memory_id = Column(UUID, ForeignKey("memory_master.memories.id"), nullable=False, index=True)
    changed_by = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    old_state = Column(String, nullable=False, index=True)
    new_state = Column(String, nullable=False, index=True)
    changed_at = Column(DateTime, default=get_current_utc_time, index=True)

    __table_args__ = (
        Index('idx_history_memory_state', 'memory_id', 'new_state'),
        Index('idx_history_user_time', 'changed_by', 'changed_at'),
        {'schema': 'memory_master'}
    )


class MemoryAccessLog(Base):
    __tablename__ = "memory_access_logs"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    memory_id = Column(UUID, ForeignKey("memory_master.memories.id"), nullable=False, index=True)
    app_id = Column(UUID, ForeignKey("memory_master.apps.id"), nullable=False, index=True)
    accessed_at = Column(DateTime, default=get_current_utc_time, index=True)
    access_type = Column(String, nullable=False, index=True)
    metadata_ = Column('metadata', JSON, default=dict)

    __table_args__ = (
        Index('idx_access_memory_time', 'memory_id', 'accessed_at'),
        Index('idx_access_app_time', 'app_id', 'accessed_at'),
        {'schema': 'memory_master'}
    )


class EvolutionOperationType(enum.Enum):
    ADD = "ADD"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    NOOP = "NOOP"


class DomainType(enum.Enum):
    technical_development = "technical_development"
    business_operations = "business_operations"


class EvolutionOperation(Base):
    __tablename__ = "evolution_operations"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    user_id = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    app_id = Column(UUID, ForeignKey("memory_master.apps.id"), nullable=False, index=True)
    memory_id = Column(UUID, ForeignKey("memory_master.memories.id"), nullable=True, index=True)
    operation_type = Column(Enum(EvolutionOperationType), nullable=False, index=True)
    candidate_fact = Column(Text, nullable=False)
    existing_memory_content = Column(Text, nullable=True)
    similarity_score = Column(Float, nullable=True)
    confidence_score = Column(Float, nullable=True)
    reasoning = Column(Text, nullable=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    metadata_ = Column('metadata', JSON, default=dict)

    # Relationships
    user = relationship("User")
    app = relationship("App")
    memory = relationship("Memory")

    __table_args__ = (
        Index('idx_evolution_user_date', 'user_id', 'created_at'),
        Index('idx_evolution_app_operation', 'app_id', 'operation_type'),
        Index('idx_evolution_memory_id', 'memory_id'),
        Index('idx_evolution_operation_type', 'operation_type'),
        {'schema': 'memory_master'}
    )


class EvolutionInsight(Base):
    __tablename__ = "evolution_insights"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    user_id = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    app_id = Column(UUID, ForeignKey("memory_master.apps.id"), nullable=True, index=True)
    date = Column(DateTime, nullable=False, index=True)
    total_operations = Column(Integer, nullable=False, default=0)
    add_operations = Column(Integer, nullable=False, default=0)
    update_operations = Column(Integer, nullable=False, default=0)
    delete_operations = Column(Integer, nullable=False, default=0)
    noop_operations = Column(Integer, nullable=False, default=0)
    learning_efficiency = Column(Float, nullable=True)
    conflict_resolution_count = Column(Integer, nullable=False, default=0)
    average_confidence = Column(Float, nullable=True)
    average_similarity = Column(Float, nullable=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    updated_at = Column(DateTime, default=get_current_utc_time, onupdate=get_current_utc_time)

    # Relationships
    user = relationship("User")
    app = relationship("App")

    __table_args__ = (
        Index('idx_insights_user_date', 'user_id', 'date'),
        Index('idx_insights_app_date', 'app_id', 'date'),
        Index('idx_insights_date', 'date'),
        UniqueConstraint('user_id', 'app_id', 'date', name='uq_evolution_insights_user_app_date'),
        {'schema': 'memory_master'}
    )


class EvolutionConfiguration(Base):
    __tablename__ = "evolution_configurations"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    user_id = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    app_id = Column(UUID, ForeignKey("memory_master.apps.id"), nullable=True, index=True)
    domain_type = Column(Enum(DomainType), nullable=False, index=True)
    fact_extraction_prompt = Column(Text, nullable=False)
    memory_evolution_prompt = Column(Text, nullable=False)
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    updated_at = Column(DateTime, default=get_current_utc_time, onupdate=get_current_utc_time)
    metadata_ = Column('metadata', JSON, default=dict)

    # Relationships
    user = relationship("User")
    app = relationship("App")
    versions = relationship("ConfigurationVersion", back_populates="config", cascade="all, delete-orphan")
    noop_threshold = relationship("NoopThreshold", back_populates="config", uselist=False, cascade="all, delete-orphan")
    analytics = relationship("EvolutionAnalytics", back_populates="config")

    __table_args__ = (
        Index('idx_config_user_active', 'user_id', 'is_active'),
        Index('idx_config_app_domain', 'app_id', 'domain_type'),
        UniqueConstraint('user_id', 'app_id', 'domain_type', name='uq_evolution_config_user_app_domain'),
        {'schema': 'memory_master'}
    )


class ConfigurationVersion(Base):
    __tablename__ = "configuration_versions"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    config_id = Column(UUID, ForeignKey("memory_master.evolution_configurations.id"), nullable=False, index=True)
    version_number = Column(Integer, nullable=False)
    changes = Column(JSON, nullable=False)
    created_by = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    rollback_data = Column(JSON, nullable=True)

    # Relationships
    config = relationship("EvolutionConfiguration", back_populates="versions")
    creator = relationship("User")

    __table_args__ = (
        Index('idx_version_config_number', 'config_id', 'version_number'),
        UniqueConstraint('config_id', 'version_number', name='uq_config_version_number'),
        {'schema': 'memory_master'}
    )


class NoopThreshold(Base):
    __tablename__ = "noop_thresholds"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    config_id = Column(UUID, ForeignKey("memory_master.evolution_configurations.id"), nullable=False, index=True)
    similarity_threshold = Column(Float, nullable=False, default=0.95)
    update_threshold = Column(Float, nullable=False, default=0.85)
    content_length_min = Column(Integer, nullable=False, default=10)
    confidence_threshold = Column(Float, nullable=False, default=0.8)
    created_at = Column(DateTime, default=get_current_utc_time, index=True)
    updated_at = Column(DateTime, default=get_current_utc_time, onupdate=get_current_utc_time)

    # Relationships
    config = relationship("EvolutionConfiguration", back_populates="noop_threshold")

    __table_args__ = (
        UniqueConstraint('config_id', name='uq_noop_threshold_config'),
        {'schema': 'memory_master'}
    )


class EvolutionAnalytics(Base):
    __tablename__ = "evolution_analytics"
    id = Column(UUID, primary_key=True, default=lambda: uuid.uuid4())
    user_id = Column(UUID, ForeignKey("memory_master.users.id"), nullable=False, index=True)
    app_id = Column(UUID, ForeignKey("memory_master.apps.id"), nullable=True, index=True)
    config_id = Column(UUID, ForeignKey("memory_master.evolution_configurations.id"), nullable=True, index=True)
    operation_type = Column(Enum(EvolutionOperationType), nullable=False, index=True)
    confidence_score = Column(Float, nullable=True)
    similarity_score = Column(Float, nullable=True)
    reasoning = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=get_current_utc_time, index=True)
    processing_time_ms = Column(Integer, nullable=True)
    metadata_ = Column('metadata', JSON, default=dict)

    # Relationships
    user = relationship("User")
    app = relationship("App")
    config = relationship("EvolutionConfiguration", back_populates="analytics")

    __table_args__ = (
        Index('idx_analytics_user_timestamp', 'user_id', 'timestamp'),
        Index('idx_analytics_app_operation', 'app_id', 'operation_type'),
        Index('idx_analytics_config_timestamp', 'config_id', 'timestamp'),
        {'schema': 'memory_master'}
    )

def categorize_memory(memory: Memory, db: Session) -> None:
    """Categorize a memory using OpenAI and store the categories in the database."""
    try:
        # Get categories from OpenAI
        categories = get_categories_for_memory(memory.content)

        # Get or create categories in the database
        for category_name in categories:
            category = db.query(Category).filter(Category.name == category_name).first()
            if not category:
                category = Category(
                    name=category_name,
                    description=f"Automatically created category for {category_name}"
                )
                db.add(category)
                db.flush()  # Flush to get the category ID

            # Check if the memory-category association already exists
            existing = db.execute(
                memory_categories.select().where(
                    (memory_categories.c.memory_id == memory.id) &
                    (memory_categories.c.category_id == category.id)
                )
            ).first()

            if not existing:
                # Create the association
                db.execute(
                    memory_categories.insert().values(
                        memory_id=memory.id,
                        category_id=category.id
                    )
                )

        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error categorizing memory: {e}")


@event.listens_for(Memory, 'after_insert')
def after_memory_insert(mapper, connection, target):
    """Trigger categorization after a memory is inserted."""
    db = Session(bind=connection)
    categorize_memory(target, db)
    db.close()


@event.listens_for(Memory, 'after_update')
def after_memory_update(mapper, connection, target):
    """Trigger categorization after a memory is updated."""
    db = Session(bind=connection)
    categorize_memory(target, db)
    db.close()
