# JWT Token Configuration Guide

## Overview

This guide documents the JWT token expiration configuration for the Memory Master v2 application using Supabase authentication.

## Current Configuration Status

✅ **PROPERLY CONFIGURED**

The JWT token expiration settings are correctly configured with recommended security practices:

- **Access Token**: 1 hour (3600 seconds)
- **Refresh Token**: 7 days (604800 seconds)
- **Auto-refresh**: Enabled
- **Token Rotation**: Automatic

## Configuration Details

### Supabase Dashboard Settings

To configure JWT expiration in the Supabase Dashboard:

1. Navigate to **Supabase Dashboard** > **Settings** > **Auth**
2. Set the following values:
   - **JWT expiry**: `3600` seconds (1 hour)
   - **Refresh token expiry**: `604800` seconds (7 days)

### Environment Variables

The following environment variables are configured:

```bash
# API Environment (.env)
SUPABASE_JWT_SECRET=d124QZSveM2X1rAdcDdO6gF5a6XOvnmlqRLa8whV
JWT_ACCESS_TOKEN_EXPIRY=3600        # 1 hour
JWT_REFRESH_TOKEN_EXPIRY=604800     # 7 days

# UI Environment (.env)
NEXT_PUBLIC_SUPABASE_URL=http://*************:8000
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Client Configuration

The Supabase client is configured with optimal auth settings:

```typescript
// ui/lib/supabase.ts
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,    // ✅ Automatically refresh tokens
    persistSession: true,      // ✅ Persist session across browser restarts
    detectSessionInUrl: true,  // ✅ Handle auth redirects
    storage: localStorage,     // ✅ Use localStorage for session storage
    flowType: 'pkce'          // ✅ Enhanced security with PKCE
  },
  global: {
    headers: {
      'X-Client-Info': 'memory-master-ui'
    }
  }
})
```

## Security Benefits

### 1. Short-lived Access Tokens (1 hour)
- Reduces risk if tokens are compromised
- Limits attack window
- Forces regular token refresh

### 2. Longer-lived Refresh Tokens (7 days)
- Provides good user experience
- Reduces need for frequent re-authentication
- Balances security with usability

### 3. Automatic Token Rotation
- Tokens are automatically refreshed before expiration
- Seamless user experience
- Enhanced security through token rotation

## Token Lifecycle

```mermaid
graph TD
    A[User Login] --> B[Get Access Token + Refresh Token]
    B --> C[Access Token Valid?]
    C -->|Yes| D[API Request]
    C -->|No/Expired| E[Auto-refresh with Refresh Token]
    E --> F[Get New Access Token]
    F --> D
    E -->|Refresh Failed| G[Redirect to Login]
    D --> H[Success]
```

## Implementation Details

### Token Structure

**Access Token Claims:**
```json
{
  "aud": "authenticated",
  "exp": 1672531200,  // 1 hour from issue
  "iat": 1672527600,  // issued at
  "sub": "user-uuid",
  "email": "<EMAIL>",
  "role": "authenticated"
}
```

**Refresh Token Claims:**
```json
{
  "aud": "authenticated",
  "exp": 1673136400,  // 7 days from issue
  "iat": 1672527600,  // issued at
  "sub": "user-uuid",
  "type": "refresh"
}
```

### Error Handling

The enhanced auth operations include proper error handling:

```typescript
// ui/lib/supabase.ts
export const authOperations = {
  async refreshSession() {
    try {
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Refresh session error:', error)
        throw new Error(error.message)
      }
      
      return data
    } catch (error) {
      console.error('Refresh session failed:', error)
      throw error
    }
  }
}
```

## Testing

### Automated Testing

Run the JWT configuration test:

```bash
node test-jwt-config.js
```

This test verifies:
- JWT secret configuration
- Token expiration settings
- Token validation
- Client configuration status

### Manual Testing

1. **Login Flow Test**:
   - User logs in successfully
   - Access token expires in 1 hour
   - Refresh token expires in 7 days

2. **Token Refresh Test**:
   - Wait for access token to expire
   - Verify automatic refresh occurs
   - Confirm new tokens are issued

3. **Session Persistence Test**:
   - Close browser
   - Reopen application
   - Verify user remains logged in (if refresh token is valid)

## Troubleshooting

### Common Issues

1. **Token Refresh Failing**:
   - Check `autoRefreshToken` is enabled
   - Verify refresh token hasn't expired
   - Ensure network connectivity

2. **Session Not Persisting**:
   - Check `persistSession` is enabled
   - Verify localStorage is available
   - Check browser storage settings

3. **Auth Redirects Not Working**:
   - Ensure `detectSessionInUrl` is enabled
   - Check redirect URL configuration
   - Verify PKCE flow is supported

### Debug Commands

```bash
# Check current session
supabase.auth.getSession()

# Force token refresh
supabase.auth.refreshSession()

# Check token expiration
jwt.decode(token).exp
```

## Security Recommendations

1. **Never store JWT secrets in client-side code**
2. **Use HTTPS in production**
3. **Implement proper error handling**
4. **Monitor token refresh patterns**
5. **Use secure storage (localStorage with encryption if needed)**

## Conclusion

The JWT token configuration is properly set up with industry-standard security practices. The 1-hour access token and 7-day refresh token provide an optimal balance between security and user experience, with automatic token rotation ensuring seamless operation.

---

*Last updated: July 3, 2025*
*Configuration status: ✅ COMPLETE*