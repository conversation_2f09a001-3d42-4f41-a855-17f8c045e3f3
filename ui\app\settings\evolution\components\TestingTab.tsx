"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Play, 
  Square, 
  RotateCcw, 
  Download, 
  TestTube,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Zap
} from "lucide-react";

interface TestScenario {
  id: string;
  name: string;
  description: string;
  inputText: string;
  existingMemory: string;
  expectedOperation: "ADD" | "UPDATE" | "DELETE" | "NOOP";
  status: "pending" | "running" | "passed" | "failed";
  result?: {
    operation: string;
    confidence: number;
    reasoning: string;
    executionTime: number;
  };
}

interface TestingTabProps {
  onSettingsChange: (hasChanges: boolean) => void;
}

export function TestingTab({ onSettingsChange }: TestingTabProps) {
  const [testScenarios, setTestScenarios] = useState<TestScenario[]>([
    {
      id: "1",
      name: "Basic Fact Addition",
      description: "Test adding a new fact to empty memory",
      inputText: "The capital of France is Paris.",
      existingMemory: "",
      expectedOperation: "ADD",
      status: "pending"
    },
    {
      id: "2", 
      name: "Memory Update",
      description: "Test updating existing information with new details",
      inputText: "John Smith is 35 years old and works as a software engineer at Google.",
      existingMemory: "John Smith is 30 years old and works as a developer.",
      expectedOperation: "UPDATE",
      status: "pending"
    },
    {
      id: "3",
      name: "Redundant Information",
      description: "Test handling of duplicate information",
      inputText: "The Earth orbits around the Sun.",
      existingMemory: "The Earth revolves around the Sun in an elliptical orbit.",
      expectedOperation: "NOOP",
      status: "pending"
    },
    {
      id: "4",
      name: "Conflicting Information",
      description: "Test resolution of conflicting facts",
      inputText: "The meeting is scheduled for 3 PM tomorrow.",
      existingMemory: "The meeting is scheduled for 2 PM tomorrow.",
      expectedOperation: "UPDATE",
      status: "pending"
    }
  ]);

  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testProgress, setTestProgress] = useState(0);
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [customTest, setCustomTest] = useState({
    name: "",
    description: "",
    inputText: "",
    existingMemory: "",
    expectedOperation: "ADD" as const
  });

  const runSingleTest = async (scenarioId: string) => {
    const scenario = testScenarios.find(s => s.id === scenarioId);
    if (!scenario) return;

    // Update status to running
    setTestScenarios(prev => prev.map(s => 
      s.id === scenarioId ? { ...s, status: "running" as const } : s
    ));

    // Simulate test execution
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock test result
    const mockResult = {
      operation: scenario.expectedOperation,
      confidence: Math.random() * 0.3 + 0.7, // 70-100%
      reasoning: `Analyzed input and determined ${scenario.expectedOperation} operation is appropriate.`,
      executionTime: Math.random() * 500 + 100 // 100-600ms
    };

    const passed = mockResult.operation === scenario.expectedOperation && mockResult.confidence > 0.6;

    setTestScenarios(prev => prev.map(s => 
      s.id === scenarioId ? { 
        ...s, 
        status: passed ? "passed" as const : "failed" as const,
        result: mockResult
      } : s
    ));
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestProgress(0);

    for (let i = 0; i < testScenarios.length; i++) {
      await runSingleTest(testScenarios[i].id);
      setTestProgress(((i + 1) / testScenarios.length) * 100);
    }

    setIsRunningTests(false);
  };

  const resetTests = () => {
    setTestScenarios(prev => prev.map(s => ({ 
      ...s, 
      status: "pending" as const,
      result: undefined
    })));
    setTestProgress(0);
  };

  const addCustomTest = () => {
    if (customTest.name && customTest.inputText) {
      const newTest: TestScenario = {
        id: Date.now().toString(),
        name: customTest.name,
        description: customTest.description,
        inputText: customTest.inputText,
        existingMemory: customTest.existingMemory,
        expectedOperation: customTest.expectedOperation,
        status: "pending"
      };

      setTestScenarios(prev => [...prev, newTest]);
      setCustomTest({
        name: "",
        description: "",
        inputText: "",
        existingMemory: "",
        expectedOperation: "ADD"
      });
      onSettingsChange(true);
    }
  };

  const exportResults = () => {
    const results = testScenarios.map(scenario => ({
      name: scenario.name,
      status: scenario.status,
      expected: scenario.expectedOperation,
      actual: scenario.result?.operation,
      confidence: scenario.result?.confidence,
      executionTime: scenario.result?.executionTime,
      reasoning: scenario.result?.reasoning
    }));

    const dataStr = JSON.stringify(results, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', `evolution-test-results-${new Date().toISOString().split('T')[0]}.json`);
    linkElement.click();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed": return <CheckCircle className="h-4 w-4 text-green-400" />;
      case "failed": return <XCircle className="h-4 w-4 text-red-400" />;
      case "running": return <Clock className="h-4 w-4 text-blue-400 animate-spin" />;
      default: return <TestTube className="h-4 w-4 text-zinc-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "passed": return "bg-green-900/20 text-green-400 border-green-800";
      case "failed": return "bg-red-900/20 text-red-400 border-red-800";
      case "running": return "bg-blue-900/20 text-blue-400 border-blue-800";
      default: return "bg-zinc-900/20 text-zinc-400 border-zinc-800";
    }
  };

  const passedTests = testScenarios.filter(s => s.status === "passed").length;
  const failedTests = testScenarios.filter(s => s.status === "failed").length;
  const totalTests = testScenarios.length;

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Evolution Testing</h3>
          <p className="text-sm text-zinc-400">
            Test evolution behavior with predefined scenarios and custom inputs
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetTests}
            disabled={isRunningTests}
            className="border-zinc-700"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>
          <Button
            variant="outline"
            onClick={exportResults}
            disabled={isRunningTests || passedTests + failedTests === 0}
            className="border-zinc-700"
          >
            <Download className="mr-2 h-4 w-4" />
            Export Results
          </Button>
          <Button
            onClick={runAllTests}
            disabled={isRunningTests}
            className="bg-primary hover:bg-primary/90"
          >
            {isRunningTests ? (
              <>
                <Square className="mr-2 h-4 w-4" />
                Running...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Run All Tests
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Test Progress */}
      {isRunningTests && (
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Test Progress</span>
                <span>{Math.round(testProgress)}%</span>
              </div>
              <Progress value={testProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results Summary */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-zinc-400">Passed</p>
                <p className="text-2xl font-bold text-green-400">{passedTests}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-zinc-400">Failed</p>
                <p className="text-2xl font-bold text-red-400">{failedTests}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-zinc-900/50 border-zinc-800">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-zinc-400">Total</p>
                <p className="text-2xl font-bold">{totalTests}</p>
              </div>
              <TestTube className="h-8 w-8 text-zinc-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Scenarios */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle>Test Scenarios</CardTitle>
          <CardDescription>
            Predefined scenarios to validate evolution behavior
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testScenarios.map((scenario) => (
              <div key={scenario.id} className="border border-zinc-800 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(scenario.status)}
                    <div>
                      <h4 className="font-medium">{scenario.name}</h4>
                      <p className="text-sm text-zinc-400">{scenario.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(scenario.status)}>
                      {scenario.status}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => runSingleTest(scenario.id)}
                      disabled={isRunningTests || scenario.status === "running"}
                      className="border-zinc-700"
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                {scenario.result && (
                  <div className="bg-zinc-800/50 rounded p-3 space-y-2 text-sm">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-zinc-400">Expected:</span> {scenario.expectedOperation}
                      </div>
                      <div>
                        <span className="text-zinc-400">Actual:</span> {scenario.result.operation}
                      </div>
                      <div>
                        <span className="text-zinc-400">Confidence:</span> {(scenario.result.confidence * 100).toFixed(1)}%
                      </div>
                      <div>
                        <span className="text-zinc-400">Time:</span> {scenario.result.executionTime.toFixed(0)}ms
                      </div>
                    </div>
                    <div>
                      <span className="text-zinc-400">Reasoning:</span> {scenario.result.reasoning}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Add Custom Test */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Add Custom Test
          </CardTitle>
          <CardDescription>
            Create a custom test scenario to validate specific behavior
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="test-name">Test Name</Label>
              <Input
                id="test-name"
                value={customTest.name}
                onChange={(e) => setCustomTest(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter test name"
                className="bg-zinc-800 border-zinc-700"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="expected-operation">Expected Operation</Label>
              <Select
                value={customTest.expectedOperation}
                onValueChange={(value) => setCustomTest(prev => ({ ...prev, expectedOperation: value as any }))}
              >
                <SelectTrigger className="bg-zinc-800 border-zinc-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADD">ADD</SelectItem>
                  <SelectItem value="UPDATE">UPDATE</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                  <SelectItem value="NOOP">NOOP</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="test-description">Description</Label>
            <Input
              id="test-description"
              value={customTest.description}
              onChange={(e) => setCustomTest(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this test validates"
              className="bg-zinc-800 border-zinc-700"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="input-text">Input Text</Label>
            <Textarea
              id="input-text"
              value={customTest.inputText}
              onChange={(e) => setCustomTest(prev => ({ ...prev, inputText: e.target.value }))}
              placeholder="Enter the new information to process"
              className="bg-zinc-800 border-zinc-700"
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="existing-memory">Existing Memory (Optional)</Label>
            <Textarea
              id="existing-memory"
              value={customTest.existingMemory}
              onChange={(e) => setCustomTest(prev => ({ ...prev, existingMemory: e.target.value }))}
              placeholder="Enter existing memory content (leave empty for new memories)"
              className="bg-zinc-800 border-zinc-700"
              rows={3}
            />
          </div>
          
          <Button
            onClick={addCustomTest}
            disabled={!customTest.name || !customTest.inputText}
            className="bg-primary hover:bg-primary/90"
          >
            <TestTube className="mr-2 h-4 w-4" />
            Add Test Scenario
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
