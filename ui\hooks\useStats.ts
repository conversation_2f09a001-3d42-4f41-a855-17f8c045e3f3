import { useState } from 'react';
import { apiGet, buildApiUrl, ApiError } from '@/lib/api-client';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { setApps, setTotalApps } from '@/store/profileSlice';
import { setTotalMemories } from '@/store/profileSlice';

// Define the new simplified memory type
export interface SimpleMemory {
  id: string;
  text: string;
  created_at: string;
  state: string;
  categories: string[];
  app_name: string;
}

// Define the shape of the API response item
interface APIStatsResponse {
  total_memories: number;
  total_apps: number;
  apps: any[];
}


interface UseMemoriesApiReturn {
  fetchStats: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export const useStats = (): UseMemoriesApiReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const user_id = useSelector((state: RootState) => state.profile.userId);


  const fetchStats = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiGet<APIStatsResponse>(
        buildApiUrl('/api/v1/stats')
      );
      dispatch(setTotalMemories(response.total_memories));
      dispatch(setTotalApps(response.total_apps));
      dispatch(setApps(response.apps));
      setIsLoading(false);
    } catch (err: any) {
      const errorMessage = err instanceof ApiError ? err.message : (err.message || 'Failed to fetch stats');
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  return { fetchStats, isLoading, error };
};