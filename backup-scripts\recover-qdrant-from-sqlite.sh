#!/bin/bash
# recover-qdrant-from-sqlite.sh - Recover Qdrant collections from SQLite database

set -e

LOG_FILE="/home/<USER>/memory-master-v2/logs/recovery.log"
PROJECT_DIR="/home/<USER>/memory-master-v2"

echo "$(date): 🔄 Starting Qdrant recovery from SQLite..." | tee -a $LOG_FILE

cd $PROJECT_DIR

# Check if containers are running
if ! docker ps | grep -q memory-mcp; then
    echo "$(date): Starting containers..." | tee -a $LOG_FILE
    docker-compose up -d
    sleep 15
fi

# Create recovery script inside the container
echo "$(date): Creating recovery script..." | tee -a $LOG_FILE

docker exec memory-mcp python3 -c "
import sqlite3
import json
import requests
import time
from app.database import SessionLocal
from app.models import Memory, MemoryState
from app.utils.memory import get_memory_client

print('🔄 Starting Qdrant recovery from SQLite...')

# Wait for services to be ready
time.sleep(5)

# Get database session
db = SessionLocal()

try:
    # Get memory client
    memory_client = get_memory_client()
    if not memory_client:
        print('❌ Memory client not available')
        exit(1)
    
    print('✅ Memory client connected')
    
    # Get all active memories from SQLite
    memories = db.query(Memory).filter(
        Memory.state == MemoryState.active
    ).all()
    
    print(f'📊 Found {len(memories)} active memories in SQLite')
    
    recovered_count = 0
    failed_count = 0
    
    for memory in memories:
        try:
            # Add memory to Qdrant with the same ID
            print(f'🔄 Recovering memory: {memory.id}')
            
            # Prepare metadata
            metadata = {
                'source_app': 'openmemory',
                'app_id': str(memory.app_id),
                'user_id': str(memory.user_id),
                'created_at': memory.created_at.isoformat() if memory.created_at else None
            }
            
            # Add any existing metadata
            if memory.metadata_:
                metadata.update(memory.metadata_)
            
            # Add to Qdrant (let it generate new ID, then we'll sync)
            result = memory_client.add(
                memory.content,
                user_id=str(memory.user_id),
                metadata=metadata
            )
            
            # Get the new Qdrant ID and update the SQLite record if needed
            if isinstance(result, dict) and 'memory_id' in result:
                new_qdrant_id = result['memory_id']
                print(f'✅ Recovered memory {memory.id} -> Qdrant ID: {new_qdrant_id}')
            else:
                print(f'✅ Recovered memory {memory.id} (no ID returned)')
            
            print(f'✅ Recovered memory {memory.id}')
            recovered_count += 1
            
        except Exception as e:
            print(f'❌ Failed to recover memory {memory.id}: {str(e)}')
            failed_count += 1
            continue
    
    print(f'📊 Recovery Summary:')
    print(f'  ✅ Successfully recovered: {recovered_count} memories')
    print(f'  ❌ Failed to recover: {failed_count} memories')
    print(f'  📈 Success rate: {(recovered_count/(recovered_count+failed_count)*100):.1f}%' if (recovered_count+failed_count) > 0 else '0%')
    
finally:
    db.close()

print('🎉 Qdrant recovery completed!')
" 2>&1 | tee -a $LOG_FILE

echo "$(date): ✅ Qdrant recovery from SQLite completed!" | tee -a $LOG_FILE