# Memory Master v2 Automated Backup Cron Jobs
# Daily backup at 2 AM
0 2 * * * /home/<USER>/memory-master-v2/backup-scripts/automated-backup.sh

# Weekly cleanup on Sundays at 3 AM (remove backups older than 30 days)
0 3 * * 0 /bin/bash -c 'docker exec memory-backup-storage mc rm --recursive --force --older-than 30d remoteminio/memory-master-dev-backups/memory-master-backup-*.tar.gz >> /home/<USER>/memory-master-v2/logs/backup.log 2>&1 && curl -s -X POST -H "Content-Type: text/plain" -d "🧹 Old Memory-Master backups cleaned up." https://workflow.syncrobit.net/webhook/v1/send-telegram-notification || curl -s -X POST -H "Content-Type: text/plain" -d "❌ Failed to clean up old Memory-Master backups." https://workflow.syncrobit.net/webhook/v1/send-telegram-notification'

# Weekly verification on Sundays at 4 AM
0 4 * * 0 /home/<USER>/memory-master-v2/backup-scripts/verify-backup.sh
