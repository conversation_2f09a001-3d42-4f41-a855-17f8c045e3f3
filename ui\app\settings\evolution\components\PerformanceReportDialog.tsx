"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Download,
  Share,
  Calendar,
  Clock,
  BarChart3,
  Brain,
  Target,
  Zap,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react";

interface PerformanceReportDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MetricData {
  value: number;
  trend: "up" | "down" | "neutral";
  trendValue: number;
  description: string;
  status: "excellent" | "good" | "warning" | "critical";
}

export function PerformanceReportDialog({ isOpen, onClose }: PerformanceReportDialogProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  // Mock performance data
  const reportData = {
    generatedAt: new Date().toLocaleString(),
    timeframe: "Last 30 Days",
    metrics: {
      learningEfficiency: {
        value: 78,
        trend: "up" as const,
        trendValue: 12,
        description: "Memory consolidation and learning rate",
        status: "good" as const
      },
      memoryQuality: {
        value: 92,
        trend: "up" as const,
        trendValue: 5,
        description: "Average quality score of stored memories",
        status: "excellent" as const
      },
      operationSuccess: {
        value: 85,
        trend: "neutral" as const,
        trendValue: 0,
        description: "Successful evolution operations rate",
        status: "good" as const
      },
      systemUptime: {
        value: 99.7,
        trend: "up" as const,
        trendValue: 0.3,
        description: "System availability and reliability",
        status: "excellent" as const
      }
    },
    insights: [
      {
        type: "success",
        title: "Excellent Memory Quality",
        description: "Your memory quality score of 92% is well above the recommended threshold of 80%."
      },
      {
        type: "info",
        title: "Learning Efficiency Improvement",
        description: "Learning efficiency has increased by 12% this month, indicating better memory consolidation."
      },
      {
        type: "warning",
        title: "Operation Success Rate",
        description: "Consider reviewing failed operations to improve the 85% success rate."
      }
    ],
    recommendations: [
      "Consider increasing memory consolidation frequency during peak usage hours",
      "Review custom prompt configurations for optimization opportunities",
      "Monitor vector store performance during high-load periods",
      "Implement automated cleanup for low-quality memories older than 6 months"
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent": return "text-green-400";
      case "good": return "text-blue-400";
      case "warning": return "text-yellow-400";
      case "critical": return "text-red-400";
      default: return "text-zinc-400";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "excellent": return <Badge className="bg-green-900/50 text-green-400 border-green-700">Excellent</Badge>;
      case "good": return <Badge className="bg-blue-900/50 text-blue-400 border-blue-700">Good</Badge>;
      case "warning": return <Badge className="bg-yellow-900/50 text-yellow-400 border-yellow-700">Warning</Badge>;
      case "critical": return <Badge className="bg-red-900/50 text-red-400 border-red-700">Critical</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTrendIcon = (trend: string, trendValue: number) => {
    if (trend === "up") return <TrendingUp className="h-4 w-4 text-green-400" />;
    if (trend === "down") return <TrendingDown className="h-4 w-4 text-red-400" />;
    return <Minus className="h-4 w-4 text-zinc-400" />;
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "success": return <CheckCircle className="h-5 w-5 text-green-400" />;
      case "warning": return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case "info": return <Info className="h-5 w-5 text-blue-400" />;
      default: return <Info className="h-5 w-5 text-zinc-400" />;
    }
  };

  const handleDownloadReport = () => {
    const reportJson = JSON.stringify(reportData, null, 2);
    const blob = new Blob([reportJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-zinc-900 border-zinc-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <BarChart3 className="h-6 w-6 text-blue-400" />
            Performance Report
          </DialogTitle>
          <DialogDescription className="flex items-center gap-4 text-zinc-400">
            <span className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {reportData.timeframe}
            </span>
            <span className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              Generated: {reportData.generatedAt}
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(reportData.metrics).map(([key, metric]) => (
              <Card key={key} className="bg-zinc-800/50 border-zinc-700">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </CardTitle>
                    {getStatusBadge(metric.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-2xl font-bold">{metric.value}%</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(metric.trend, metric.trendValue)}
                      {metric.trendValue > 0 && (
                        <span className={`text-sm ${metric.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                          {metric.trendValue}%
                        </span>
                      )}
                    </div>
                  </div>
                  <Progress value={metric.value} className="h-2 mb-2" />
                  <p className="text-xs text-zinc-400">{metric.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Insights */}
          <Card className="bg-zinc-800/50 border-zinc-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Key Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {reportData.insights.map((insight, index) => (
                <div key={index} className="flex gap-3 p-3 rounded-lg bg-zinc-900/50">
                  {getInsightIcon(insight.type)}
                  <div>
                    <h4 className="font-medium text-sm">{insight.title}</h4>
                    <p className="text-sm text-zinc-400 mt-1">{insight.description}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card className="bg-zinc-800/50 border-zinc-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {reportData.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm">
                    <Zap className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                    <span className="text-zinc-300">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-between items-center pt-4 border-t border-zinc-800">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadReport}
                className="border-zinc-700 hover:bg-zinc-800"
              >
                <Download className="h-4 w-4 mr-2" />
                Download JSON
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-zinc-700 hover:bg-zinc-800"
              >
                <Share className="h-4 w-4 mr-2" />
                Share Report
              </Button>
            </div>
            <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
