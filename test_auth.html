<!DOCTYPE html>
<html>
<head>
    <title>Memory Master Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
        .card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { color: red; margin-top: 10px; }
        .success { color: green; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="card">
        <h1>Memory Master Login Test</h1>
        <p>This simulates your Supabase login form to test authentication issues.</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="testpassword" required>
            </div>
            
            <button type="submit">Sign In</button>
        </form>
        
        <div id="result"></div>
        
        <h3>Debug Information:</h3>
        <pre id="debug"></pre>
    </div>

    <script>
        // Your actual Supabase configuration
        const SUPABASE_URL = 'http://*************:8000';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';
        
        function updateDebug(info) {
            document.getElementById('debug').textContent = JSON.stringify(info, null, 2);
        }
        
        function showResult(message, isError = false) {
            const result = document.getElementById('result');
            result.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showResult('Testing authentication...', false);
            
            try {
                // Test 1: Check if Supabase URL is reachable
                updateDebug({ step: 'Testing Supabase connectivity', url: SUPABASE_URL });
                
                const healthResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (!healthResponse.ok) {
                    throw new Error(`Supabase not reachable: ${healthResponse.status}`);
                }
                
                // Test 2: Try authentication
                updateDebug({ step: 'Attempting authentication', email, supabaseStatus: 'reachable' });
                
                const authResponse = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
                    method: 'POST',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const authResult = await authResponse.json();
                
                if (authResponse.ok) {
                    showResult('✅ Authentication successful!', false);
                    updateDebug({ 
                        step: 'Authentication successful', 
                        user: authResult.user?.email,
                        hasAccessToken: !!authResult.access_token
                    });
                } else {
                    showResult(`❌ Authentication failed: ${authResult.error_description || authResult.message}`, true);
                    updateDebug({ 
                        step: 'Authentication failed', 
                        error: authResult,
                        status: authResponse.status
                    });
                }
                
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, true);
                updateDebug({ 
                    step: 'Connection error', 
                    error: error.message,
                    stack: error.stack
                });
            }
        });
        
        // Initialize debug info
        updateDebug({ 
            supabaseUrl: SUPABASE_URL,
            hasAnonymousKey: !!SUPABASE_ANON_KEY,
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>