import datetime
from fastapi import Fast<PERSON><PERSON>
from app.database import engine, Base, SessionLocal
from app.routers import memories_router, apps_router, stats_router, config_router, health_router
from app.routers.auth import router as auth_router
from app.routers.evolution_config import router as evolution_config_router
from fastapi_pagination import add_pagination
from fastapi.middleware.cors import CORSMiddleware
from app.middleware.security import security_middleware
from app.models import User, App
from uuid import uuid4
from app.config import USER_ID, DEFAULT_APP_ID
import logging

# Configure logging for security monitoring
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = FastAPI(
    title="OpenMemory API",
    description="Memory Master API with enhanced security features",
    version="2.0.0"
)

origins = [
    "http://localhost:3000",
    "http://************:3000",
    "http://127.0.0.1:3000",
    "*"
]

# Add security middleware first (before CORS)
# app.add_middleware(security_middleware)  # Disabled for internal tool usage

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create all tables
Base.metadata.create_all(bind=engine)

# Check for USER_ID and create default user if needed
def create_default_user():
    db = SessionLocal()
    try:
        # Check if user exists
        user = db.query(User).filter(User.user_id == USER_ID).first()
        if not user:
            # Create default user
            user = User(
                id=uuid4(),
                user_id=USER_ID,
                name="Default User",
                created_at=datetime.datetime.now(datetime.UTC)
            )
            db.add(user)
            db.commit()
    finally:
        db.close()


def create_default_app():
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.user_id == USER_ID).first()
        if not user:
            return

        # Check if app already exists
        existing_app = db.query(App).filter(
            App.name == DEFAULT_APP_ID,
            App.owner_id == user.id
        ).first()

        if existing_app:
            return

        app = App(
            id=uuid4(),
            name=DEFAULT_APP_ID,
            owner_id=user.id,
            created_at=datetime.datetime.now(datetime.UTC),
            updated_at=datetime.datetime.now(datetime.UTC),
        )
        db.add(app)
        db.commit()
    finally:
        db.close()

# Create default user on startup
create_default_user()
create_default_app()

# Initialize configuration hot-reload system
from app.utils.config_init import initialize_config_system
initialize_config_system()

# Setup MCP server - simplified approach matching original mem0ai implementation
from app.mcp_server import mcp_router

# Include the MCP router which now handles all SSE endpoints directly
app.include_router(mcp_router)

# Include routers
app.include_router(auth_router)
app.include_router(memories_router)
app.include_router(apps_router)
app.include_router(stats_router)
app.include_router(config_router)
app.include_router(health_router, prefix="/api/v1")
app.include_router(evolution_config_router, prefix="/api/v1")

# Add pagination support
add_pagination(app)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.datetime.now(datetime.UTC)}
