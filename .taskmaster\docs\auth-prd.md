# Memory Master Authentication PRD (Simplified)

## 1. Overview

Replace hardcoded usernames with Supabase authentication so <PERSON><PERSON> and <PERSON><PERSON> can securely access their own memories through proper login.

## 2. Current Problems

- Username hardcoded in `NEXT_PUBLIC_USER_ID`
- No login required - anyone can view all memories
- MCP configs have hardcoded usernames
- Adding users requires code changes

## 3. Implementation Plan

### 3.1 Core Features

1. **Login Page**
   - Email/password login via Supabase
   - Logout button in navigation
   - Redirect unauthorized users to login

2. **User-Specific Memories**
   - Filter memories by logged-in user
   - API validates user from auth token
   - Each user sees only their data

3. **Dynamic MCP Configuration**
   - Installation page shows user's personalized URLs
   - Automatically uses authenticated username
   - Copy buttons for easy setup

### 3.2 Technical Changes

**Frontend (UI)**
- Add login/logout pages
- Protect routes with auth check
- Store auth token and user info
- Update API calls to include auth

**Backend (API)**
- Add auth middleware to validate tokens
- Extract username from Supabase JWT
- Filter database queries by authenticated user
- Update MCP validation to use auth username

### 3.3 Setup Steps

1. Create 2 users in Supabase:
   - <PERSON><PERSON>'s account
   - Yohanna's account

2. Update environment variables:
   - Remove `NEXT_PUBLIC_USER_ID`
   - Ensure Supabase keys are configured

3. Deploy changes and test both accounts

## 4. Testing Checklist

- [ ] Aung can login and see only his memories
- [ ] Yohanna can login and see only her memories
- [ ] Logout works and redirects to login
- [ ] MCP configs show correct username
- [ ] Unauthorized access blocked
- [ ] Existing MCP setups still work

## 5. Success Criteria

- Both users login with their Supabase credentials
- Each user accesses only their own memories
- MCP configurations automatically personalized
- No more hardcoded usernames in code