import { useState, useCallback } from 'react'
import { apiGet, apiPut, buildApiUrl, ApiError } from '@/lib/api-client'

interface AuthStatus {
  auth_enabled: boolean
  is_authenticated: boolean
  user: {
    user_id: string
    email: string
    name?: string
    email_verified: boolean
    is_authenticated: boolean
    supabase_user_id?: string
    created_at?: string
    last_sign_in_at?: string
  } | null
}

interface UserProfile {
  user_id: string
  email: string
  name?: string
  email_verified: boolean
  is_authenticated: boolean
  supabase_user_id?: string
  created_at?: string
  last_sign_in_at?: string
}

interface UpdateProfileRequest {
  name?: string
}

export function useAuthApi() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)


  const fetchAuthStatus = useCallback(async (): Promise<AuthStatus> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await apiGet<AuthStatus>(buildApiUrl('/auth/status'))
      return response
    } catch (err) {
      const errorMessage = err instanceof ApiError ? err.message : (err instanceof Error ? err.message : 'Failed to fetch auth status')
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchUserProfile = useCallback(async (): Promise<UserProfile> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await apiGet<UserProfile>(buildApiUrl('/auth/profile'))
      return response
    } catch (err) {
      const errorMessage = err instanceof ApiError ? err.message : (err instanceof Error ? err.message : 'Failed to fetch user profile')
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const updateUserProfile = useCallback(async (data: UpdateProfileRequest): Promise<UserProfile> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await apiPut<UserProfile>(buildApiUrl('/auth/profile'), data)
      return response
    } catch (err) {
      const errorMessage = err instanceof ApiError ? err.message : (err instanceof Error ? err.message : 'Failed to update user profile')
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    error,
    fetchAuthStatus,
    fetchUserProfile,
    updateUserProfile
  }
}