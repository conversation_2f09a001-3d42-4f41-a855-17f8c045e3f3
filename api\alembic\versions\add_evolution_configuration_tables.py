"""Add evolution configuration tables

Revision ID: a1b2c3d4e5f6
Revises: e7f8a9b0c1d2
Create Date: 2025-06-30 04:15:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, None] = 'e7f8a9b0c1d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add evolution configuration tables."""
    
    # Create domain_type enum
    op.execute("CREATE TYPE memory_master.domaintype AS ENUM ('technical_development', 'business_operations')")
    
    # Create evolution_configurations table
    op.create_table(
        'evolution_configurations',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('app_id', sa.UUID(), nullable=True),
        sa.Column('domain_type', sa.Enum('technical_development', 'business_operations', name='domaintype'), nullable=False),
        sa.Column('fact_extraction_prompt', sa.Text(), nullable=False),
        sa.Column('memory_evolution_prompt', sa.Text(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['app_id'], ['memory_master.apps.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['memory_master.users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'app_id', 'domain_type', name='uq_evolution_config_user_app_domain'),
        schema='memory_master'
    )
    
    # Create indexes for evolution_configurations
    op.create_index('idx_config_user_active', 'evolution_configurations', ['user_id', 'is_active'], unique=False, schema='memory_master')
    op.create_index('idx_config_app_domain', 'evolution_configurations', ['app_id', 'domain_type'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_configurations_user_id'), 'evolution_configurations', ['user_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_configurations_app_id'), 'evolution_configurations', ['app_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_configurations_created_at'), 'evolution_configurations', ['created_at'], unique=False, schema='memory_master')
    
    # Create configuration_versions table
    op.create_table(
        'configuration_versions',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('config_id', sa.UUID(), nullable=False),
        sa.Column('version_number', sa.Integer(), nullable=False),
        sa.Column('changes', sa.JSON(), nullable=False),
        sa.Column('created_by', sa.UUID(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('rollback_data', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['config_id'], ['memory_master.evolution_configurations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['memory_master.users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('config_id', 'version_number', name='uq_config_version_number'),
        schema='memory_master'
    )
    
    # Create indexes for configuration_versions
    op.create_index('idx_version_config_number', 'configuration_versions', ['config_id', 'version_number'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_configuration_versions_config_id'), 'configuration_versions', ['config_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_configuration_versions_created_at'), 'configuration_versions', ['created_at'], unique=False, schema='memory_master')
    
    # Create noop_thresholds table
    op.create_table(
        'noop_thresholds',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('config_id', sa.UUID(), nullable=False),
        sa.Column('similarity_threshold', sa.Float(), nullable=False, default=0.95),
        sa.Column('update_threshold', sa.Float(), nullable=False, default=0.85),
        sa.Column('content_length_min', sa.Integer(), nullable=False, default=10),
        sa.Column('confidence_threshold', sa.Float(), nullable=False, default=0.8),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['config_id'], ['memory_master.evolution_configurations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('config_id', name='uq_noop_threshold_config'),
        schema='memory_master'
    )
    
    # Create indexes for noop_thresholds
    op.create_index(op.f('ix_memory_master_noop_thresholds_config_id'), 'noop_thresholds', ['config_id'], unique=False, schema='memory_master')
    
    # Create evolution_analytics table (enhanced version of existing insights)
    op.create_table(
        'evolution_analytics',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('app_id', sa.UUID(), nullable=True),
        sa.Column('config_id', sa.UUID(), nullable=True),
        sa.Column('operation_type', postgresql.ENUM('ADD', 'UPDATE', 'DELETE', 'NOOP', name='evolutionoperationtype', schema='memory_master'), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('similarity_score', sa.Float(), nullable=True),
        sa.Column('reasoning', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('processing_time_ms', sa.Integer(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['app_id'], ['memory_master.apps.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['config_id'], ['memory_master.evolution_configurations.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['user_id'], ['memory_master.users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        schema='memory_master'
    )
    
    # Create indexes for evolution_analytics
    op.create_index('idx_analytics_user_timestamp', 'evolution_analytics', ['user_id', 'timestamp'], unique=False, schema='memory_master')
    op.create_index('idx_analytics_app_operation', 'evolution_analytics', ['app_id', 'operation_type'], unique=False, schema='memory_master')
    op.create_index('idx_analytics_config_timestamp', 'evolution_analytics', ['config_id', 'timestamp'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_analytics_user_id'), 'evolution_analytics', ['user_id'], unique=False, schema='memory_master')
    op.create_index(op.f('ix_memory_master_evolution_analytics_timestamp'), 'evolution_analytics', ['timestamp'], unique=False, schema='memory_master')


def downgrade() -> None:
    """Downgrade schema to remove evolution configuration tables."""
    
    # Drop evolution_analytics table and its indexes
    op.drop_index(op.f('ix_memory_master_evolution_analytics_timestamp'), table_name='evolution_analytics', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_analytics_user_id'), table_name='evolution_analytics', schema='memory_master')
    op.drop_index('idx_analytics_config_timestamp', table_name='evolution_analytics', schema='memory_master')
    op.drop_index('idx_analytics_app_operation', table_name='evolution_analytics', schema='memory_master')
    op.drop_index('idx_analytics_user_timestamp', table_name='evolution_analytics', schema='memory_master')
    op.drop_table('evolution_analytics', schema='memory_master')
    
    # Drop noop_thresholds table and its indexes
    op.drop_index(op.f('ix_memory_master_noop_thresholds_config_id'), table_name='noop_thresholds', schema='memory_master')
    op.drop_table('noop_thresholds', schema='memory_master')
    
    # Drop configuration_versions table and its indexes
    op.drop_index(op.f('ix_memory_master_configuration_versions_created_at'), table_name='configuration_versions', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_configuration_versions_config_id'), table_name='configuration_versions', schema='memory_master')
    op.drop_index('idx_version_config_number', table_name='configuration_versions', schema='memory_master')
    op.drop_table('configuration_versions', schema='memory_master')
    
    # Drop evolution_configurations table and its indexes
    op.drop_index(op.f('ix_memory_master_evolution_configurations_created_at'), table_name='evolution_configurations', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_configurations_app_id'), table_name='evolution_configurations', schema='memory_master')
    op.drop_index(op.f('ix_memory_master_evolution_configurations_user_id'), table_name='evolution_configurations', schema='memory_master')
    op.drop_index('idx_config_app_domain', table_name='evolution_configurations', schema='memory_master')
    op.drop_index('idx_config_user_active', table_name='evolution_configurations', schema='memory_master')
    op.drop_table('evolution_configurations', schema='memory_master')
    
    # Drop the enum type
    op.execute('DROP TYPE IF EXISTS memory_master.domaintype')