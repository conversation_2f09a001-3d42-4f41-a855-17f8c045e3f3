"""Initial migration

Revision ID: 0b53c747049a
Revises: 
Create Date: 2025-04-19 00:59:56.244203

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0b53c747049a'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('access_controls',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('subject_type', sa.String(), nullable=False),
    sa.Column('subject_id', sa.UUID(), nullable=True),
    sa.Column('object_type', sa.String(), nullable=False),
    sa.Column('object_id', sa.UUID(), nullable=True),
    sa.Column('effect', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_access_object', 'access_controls', ['object_type', 'object_id'], unique=False)
    op.create_index('idx_access_subject', 'access_controls', ['subject_type', 'subject_id'], unique=False)
    op.create_index(op.f('ix_access_controls_created_at'), 'access_controls', ['created_at'], unique=False)
    op.create_index(op.f('ix_access_controls_effect'), 'access_controls', ['effect'], unique=False)
    op.create_index(op.f('ix_access_controls_object_id'), 'access_controls', ['object_id'], unique=False)
    op.create_index(op.f('ix_access_controls_object_type'), 'access_controls', ['object_type'], unique=False)
    op.create_index(op.f('ix_access_controls_subject_id'), 'access_controls', ['subject_id'], unique=False)
    op.create_index(op.f('ix_access_controls_subject_type'), 'access_controls', ['subject_type'], unique=False)
    op.create_table('archive_policies',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('criteria_type', sa.String(), nullable=False),
    sa.Column('criteria_id', sa.UUID(), nullable=True),
    sa.Column('days_to_archive', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_policy_criteria', 'archive_policies', ['criteria_type', 'criteria_id'], unique=False)
    op.create_index(op.f('ix_archive_policies_created_at'), 'archive_policies', ['created_at'], unique=False)
    op.create_index(op.f('ix_archive_policies_criteria_id'), 'archive_policies', ['criteria_id'], unique=False)
    op.create_index(op.f('ix_archive_policies_criteria_type'), 'archive_policies', ['criteria_type'], unique=False)
    op.create_table('categories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_categories_created_at'), 'categories', ['created_at'], unique=False)
    op.create_index(op.f('ix_categories_name'), 'categories', ['name'], unique=True)
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_created_at'), 'users', ['created_at'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_name'), 'users', ['name'], unique=False)
    op.create_index(op.f('ix_users_user_id'), 'users', ['user_id'], unique=True)
    op.create_table('apps',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_apps_created_at'), 'apps', ['created_at'], unique=False)
    op.create_index(op.f('ix_apps_is_active'), 'apps', ['is_active'], unique=False)
    op.create_index(op.f('ix_apps_name'), 'apps', ['name'], unique=True)
    op.create_index(op.f('ix_apps_owner_id'), 'apps', ['owner_id'], unique=False)
    op.create_table('memories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('app_id', sa.UUID(), nullable=False),
    sa.Column('content', sa.String(), nullable=False),
    sa.Column('vector', sa.String(), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('state', sa.Enum('active', 'paused', 'archived', 'deleted', name='memorystate'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('archived_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['app_id'], ['apps.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_memory_app_state', 'memories', ['app_id', 'state'], unique=False)
    op.create_index('idx_memory_user_app', 'memories', ['user_id', 'app_id'], unique=False)
    op.create_index('idx_memory_user_state', 'memories', ['user_id', 'state'], unique=False)
    op.create_index(op.f('ix_memories_app_id'), 'memories', ['app_id'], unique=False)
    op.create_index(op.f('ix_memories_archived_at'), 'memories', ['archived_at'], unique=False)
    op.create_index(op.f('ix_memories_created_at'), 'memories', ['created_at'], unique=False)
    op.create_index(op.f('ix_memories_deleted_at'), 'memories', ['deleted_at'], unique=False)
    op.create_index(op.f('ix_memories_state'), 'memories', ['state'], unique=False)
    op.create_index(op.f('ix_memories_user_id'), 'memories', ['user_id'], unique=False)
    op.create_table('memory_access_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('memory_id', sa.UUID(), nullable=False),
    sa.Column('app_id', sa.UUID(), nullable=False),
    sa.Column('accessed_at', sa.DateTime(), nullable=True),
    sa.Column('access_type', sa.String(), nullable=False),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['app_id'], ['apps.id'], ),
    sa.ForeignKeyConstraint(['memory_id'], ['memories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_access_app_time', 'memory_access_logs', ['app_id', 'accessed_at'], unique=False)
    op.create_index('idx_access_memory_time', 'memory_access_logs', ['memory_id', 'accessed_at'], unique=False)
    op.create_index(op.f('ix_memory_access_logs_access_type'), 'memory_access_logs', ['access_type'], unique=False)
    op.create_index(op.f('ix_memory_access_logs_accessed_at'), 'memory_access_logs', ['accessed_at'], unique=False)
    op.create_index(op.f('ix_memory_access_logs_app_id'), 'memory_access_logs', ['app_id'], unique=False)
    op.create_index(op.f('ix_memory_access_logs_memory_id'), 'memory_access_logs', ['memory_id'], unique=False)
    op.create_table('memory_categories',
    sa.Column('memory_id', sa.UUID(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.ForeignKeyConstraint(['memory_id'], ['memories.id'], ),
    sa.PrimaryKeyConstraint('memory_id', 'category_id')
    )
    op.create_index('idx_memory_category', 'memory_categories', ['memory_id', 'category_id'], unique=False)
    op.create_index(op.f('ix_memory_categories_category_id'), 'memory_categories', ['category_id'], unique=False)
    op.create_index(op.f('ix_memory_categories_memory_id'), 'memory_categories', ['memory_id'], unique=False)
    op.create_table('memory_status_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('memory_id', sa.UUID(), nullable=False),
    sa.Column('changed_by', sa.UUID(), nullable=False),
    sa.Column('old_state', sa.Enum('active', 'paused', 'archived', 'deleted', name='memorystate'), nullable=False),
    sa.Column('new_state', sa.Enum('active', 'paused', 'archived', 'deleted', name='memorystate'), nullable=False),
    sa.Column('changed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['memory_id'], ['memories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_history_memory_state', 'memory_status_history', ['memory_id', 'new_state'], unique=False)
    op.create_index('idx_history_user_time', 'memory_status_history', ['changed_by', 'changed_at'], unique=False)
    op.create_index(op.f('ix_memory_status_history_changed_at'), 'memory_status_history', ['changed_at'], unique=False)
    op.create_index(op.f('ix_memory_status_history_changed_by'), 'memory_status_history', ['changed_by'], unique=False)
    op.create_index(op.f('ix_memory_status_history_memory_id'), 'memory_status_history', ['memory_id'], unique=False)
    op.create_index(op.f('ix_memory_status_history_new_state'), 'memory_status_history', ['new_state'], unique=False)
    op.create_index(op.f('ix_memory_status_history_old_state'), 'memory_status_history', ['old_state'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_memory_status_history_old_state'), table_name='memory_status_history')
    op.drop_index(op.f('ix_memory_status_history_new_state'), table_name='memory_status_history')
    op.drop_index(op.f('ix_memory_status_history_memory_id'), table_name='memory_status_history')
    op.drop_index(op.f('ix_memory_status_history_changed_by'), table_name='memory_status_history')
    op.drop_index(op.f('ix_memory_status_history_changed_at'), table_name='memory_status_history')
    op.drop_index('idx_history_user_time', table_name='memory_status_history')
    op.drop_index('idx_history_memory_state', table_name='memory_status_history')
    op.drop_table('memory_status_history')
    op.drop_index(op.f('ix_memory_categories_memory_id'), table_name='memory_categories')
    op.drop_index(op.f('ix_memory_categories_category_id'), table_name='memory_categories')
    op.drop_index('idx_memory_category', table_name='memory_categories')
    op.drop_table('memory_categories')
    op.drop_index(op.f('ix_memory_access_logs_memory_id'), table_name='memory_access_logs')
    op.drop_index(op.f('ix_memory_access_logs_app_id'), table_name='memory_access_logs')
    op.drop_index(op.f('ix_memory_access_logs_accessed_at'), table_name='memory_access_logs')
    op.drop_index(op.f('ix_memory_access_logs_access_type'), table_name='memory_access_logs')
    op.drop_index('idx_access_memory_time', table_name='memory_access_logs')
    op.drop_index('idx_access_app_time', table_name='memory_access_logs')
    op.drop_table('memory_access_logs')
    op.drop_index(op.f('ix_memories_user_id'), table_name='memories')
    op.drop_index(op.f('ix_memories_state'), table_name='memories')
    op.drop_index(op.f('ix_memories_deleted_at'), table_name='memories')
    op.drop_index(op.f('ix_memories_created_at'), table_name='memories')
    op.drop_index(op.f('ix_memories_archived_at'), table_name='memories')
    op.drop_index(op.f('ix_memories_app_id'), table_name='memories')
    op.drop_index('idx_memory_user_state', table_name='memories')
    op.drop_index('idx_memory_user_app', table_name='memories')
    op.drop_index('idx_memory_app_state', table_name='memories')
    op.drop_table('memories')
    op.drop_index(op.f('ix_apps_owner_id'), table_name='apps')
    op.drop_index(op.f('ix_apps_name'), table_name='apps')
    op.drop_index(op.f('ix_apps_is_active'), table_name='apps')
    op.drop_index(op.f('ix_apps_created_at'), table_name='apps')
    op.drop_table('apps')
    op.drop_index(op.f('ix_users_user_id'), table_name='users')
    op.drop_index(op.f('ix_users_name'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_created_at'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_categories_name'), table_name='categories')
    op.drop_index(op.f('ix_categories_created_at'), table_name='categories')
    op.drop_table('categories')
    op.drop_index(op.f('ix_archive_policies_criteria_type'), table_name='archive_policies')
    op.drop_index(op.f('ix_archive_policies_criteria_id'), table_name='archive_policies')
    op.drop_index(op.f('ix_archive_policies_created_at'), table_name='archive_policies')
    op.drop_index('idx_policy_criteria', table_name='archive_policies')
    op.drop_table('archive_policies')
    op.drop_index(op.f('ix_access_controls_subject_type'), table_name='access_controls')
    op.drop_index(op.f('ix_access_controls_subject_id'), table_name='access_controls')
    op.drop_index(op.f('ix_access_controls_object_type'), table_name='access_controls')
    op.drop_index(op.f('ix_access_controls_object_id'), table_name='access_controls')
    op.drop_index(op.f('ix_access_controls_effect'), table_name='access_controls')
    op.drop_index(op.f('ix_access_controls_created_at'), table_name='access_controls')
    op.drop_index('idx_access_subject', table_name='access_controls')
    op.drop_index('idx_access_object', table_name='access_controls')
    op.drop_table('access_controls')
    # ### end Alembic commands ###
