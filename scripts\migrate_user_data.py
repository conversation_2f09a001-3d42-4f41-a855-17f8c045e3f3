#!/usr/bin/env python3
"""
Memory Master User Data Migration Script

This script migrates data from hardcoded user system to authenticated Supabase users.
Run this script after creating Supabase users but before enabling authentication.
"""

import os
import sys
import json
import asyncio
import asyncpg
from datetime import datetime
from typing import Dict, List, Optional

# User mapping configuration
USER_MAPPING = {
    "user": "<EMAIL>",
    "admin": "<EMAIL>",
    # Add more mappings as needed
}

class DataMigrator:
    def __init__(self):
        self.db_url = os.getenv("SUPABASE_DATABASE_URL")
        if not self.db_url:
            raise ValueError("SUPABASE_DATABASE_URL environment variable is required")
        
        self.dry_run = "--dry-run" in sys.argv
        self.verbose = "--verbose" in sys.argv or "-v" in sys.argv
        
    async def connect_db(self):
        """Connect to the database"""
        try:
            self.conn = await asyncpg.connect(self.db_url)
            if self.verbose:
                print("✓ Connected to database")
        except Exception as e:
            print(f"✗ Failed to connect to database: {e}")
            sys.exit(1)
    
    async def close_db(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            await self.conn.close()
            if self.verbose:
                print("✓ Database connection closed")
    
    async def get_supabase_users(self) -> Dict[str, str]:
        """Get Supabase user IDs by email"""
        query = "SELECT id, email FROM auth.users"
        try:
            rows = await self.conn.fetch(query)
            users = {row['email']: row['id'] for row in rows}
            if self.verbose:
                print(f"✓ Found {len(users)} Supabase users")
            return users
        except Exception as e:
            print(f"✗ Failed to fetch Supabase users: {e}")
            return {}
    
    async def migrate_memories(self, user_mapping: Dict[str, str]) -> int:
        """Migrate memories to new user IDs"""
        migrated_count = 0
        
        for old_user, email in USER_MAPPING.items():
            if email not in user_mapping:
                print(f"⚠ Warning: No Supabase user found for {email}")
                continue
                
            new_user_id = user_mapping[email]
            
            # Update memories table
            query = """
                UPDATE memories 
                SET source_app = $1 
                WHERE source_app = $2
            """
            
            if self.dry_run:
                # Count how many would be updated
                count_query = "SELECT COUNT(*) FROM memories WHERE source_app = $1"
                count = await self.conn.fetchval(count_query, old_user)
                print(f"[DRY RUN] Would migrate {count} memories from '{old_user}' to '{email}' ({new_user_id})")
                migrated_count += count
            else:
                result = await self.conn.execute(query, new_user_id, old_user)
                count = int(result.split()[-1])  # Extract number from "UPDATE X"
                print(f"✓ Migrated {count} memories from '{old_user}' to '{email}' ({new_user_id})")
                migrated_count += count
        
        return migrated_count
    
    async def migrate_apps(self, user_mapping: Dict[str, str]) -> int:
        """Migrate apps to new user IDs"""
        migrated_count = 0
        
        for old_user, email in USER_MAPPING.items():
            if email not in user_mapping:
                continue
                
            new_user_id = user_mapping[email]
            
            # Update apps table
            query = """
                UPDATE apps 
                SET user_id = $1 
                WHERE user_id = $2
            """
            
            if self.dry_run:
                count_query = "SELECT COUNT(*) FROM apps WHERE user_id = $1"
                count = await self.conn.fetchval(count_query, old_user)
                print(f"[DRY RUN] Would migrate {count} apps from '{old_user}' to '{email}' ({new_user_id})")
                migrated_count += count
            else:
                result = await self.conn.execute(query, new_user_id, old_user)
                count = int(result.split()[-1])
                print(f"✓ Migrated {count} apps from '{old_user}' to '{email}' ({new_user_id})")
                migrated_count += count
        
        return migrated_count
    
    async def migrate_configurations(self, user_mapping: Dict[str, str]) -> int:
        """Migrate configuration entries to new user IDs"""
        migrated_count = 0
        
        for old_user, email in USER_MAPPING.items():
            if email not in user_mapping:
                continue
                
            new_user_id = user_mapping[email]
            
            # Update config table if it exists
            try:
                query = """
                    UPDATE config 
                    SET user_id = $1 
                    WHERE user_id = $2
                """
                
                if self.dry_run:
                    count_query = "SELECT COUNT(*) FROM config WHERE user_id = $1"
                    count = await self.conn.fetchval(count_query, old_user)
                    print(f"[DRY RUN] Would migrate {count} config entries from '{old_user}' to '{email}' ({new_user_id})")
                    migrated_count += count
                else:
                    result = await self.conn.execute(query, new_user_id, old_user)
                    count = int(result.split()[-1])
                    print(f"✓ Migrated {count} config entries from '{old_user}' to '{email}' ({new_user_id})")
                    migrated_count += count
            except asyncpg.UndefinedTableError:
                if self.verbose:
                    print("ℹ Config table not found, skipping")
        
        return migrated_count
    
    async def create_backup(self):
        """Create a backup of current data before migration"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"backup_pre_migration_{timestamp}.sql"
        
        # This would require pg_dump, which might not be available in all environments
        # For now, just log the intent
        print(f"ℹ Create manual backup using: pg_dump $SUPABASE_DATABASE_URL > {backup_file}")
    
    async def validate_migration(self, user_mapping: Dict[str, str]) -> bool:
        """Validate that migration was successful"""
        success = True
        
        for old_user, email in USER_MAPPING.items():
            if email not in user_mapping:
                continue
            
            # Check if any old user references remain
            tables_to_check = [
                ("memories", "source_app"),
                ("apps", "user_id"),
            ]
            
            for table, column in tables_to_check:
                try:
                    query = f"SELECT COUNT(*) FROM {table} WHERE {column} = $1"
                    count = await self.conn.fetchval(query, old_user)
                    if count > 0:
                        print(f"✗ Found {count} unmigrated records in {table}.{column} for '{old_user}'")
                        success = False
                    elif self.verbose:
                        print(f"✓ No unmigrated records in {table}.{column} for '{old_user}'")
                except asyncpg.UndefinedTableError:
                    if self.verbose:
                        print(f"ℹ Table {table} not found, skipping validation")
        
        return success
    
    async def run(self):
        """Run the complete migration process"""
        print("🚀 Starting Memory Master User Data Migration")
        print(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE MIGRATION'}")
        print(f"User mappings: {len(USER_MAPPING)}")
        
        await self.connect_db()
        
        try:
            # Step 1: Create backup recommendation
            await self.create_backup()
            
            # Step 2: Get Supabase users
            supabase_users = await self.get_supabase_users()
            if not supabase_users:
                print("✗ No Supabase users found. Please create users first.")
                return False
            
            # Step 3: Validate all mapped users exist
            missing_users = []
            for old_user, email in USER_MAPPING.items():
                if email not in supabase_users:
                    missing_users.append(email)
            
            if missing_users:
                print(f"✗ Missing Supabase users: {missing_users}")
                print("Please create these users before running migration.")
                return False
            
            # Step 4: Run migrations
            print("\n📊 Migrating data...")
            total_migrated = 0
            
            total_migrated += await self.migrate_memories(supabase_users)
            total_migrated += await self.migrate_apps(supabase_users)
            total_migrated += await self.migrate_configurations(supabase_users)
            
            print(f"\n✓ Migration complete! Total records migrated: {total_migrated}")
            
            # Step 5: Validate migration
            if not self.dry_run:
                print("\n🔍 Validating migration...")
                validation_success = await self.validate_migration(supabase_users)
                if validation_success:
                    print("✓ Migration validation successful")
                else:
                    print("✗ Migration validation failed")
                    return False
            
            return True
            
        finally:
            await self.close_db()

def print_usage():
    """Print usage information"""
    print("""
Usage: python migrate_user_data.py [options]

Options:
    --dry-run    Run in dry-run mode (no actual changes)
    --verbose    Enable verbose output
    -v           Short form of --verbose
    --help       Show this help message

Environment Variables:
    SUPABASE_DATABASE_URL    Required: PostgreSQL connection URL

Before running this script:
1. Create Supabase users for all emails in USER_MAPPING
2. Set SUPABASE_DATABASE_URL environment variable
3. Create a database backup
4. Test in staging environment first

Example:
    python migrate_user_data.py --dry-run --verbose
    python migrate_user_data.py
""")

async def main():
    if "--help" in sys.argv:
        print_usage()
        return
    
    try:
        migrator = DataMigrator()
        success = await migrator.run()
        
        if success:
            print("\n🎉 Migration completed successfully!")
            if not migrator.dry_run:
                print("You can now enable authentication by setting AUTH_ENABLED=true")
        else:
            print("\n💥 Migration failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Migration failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())