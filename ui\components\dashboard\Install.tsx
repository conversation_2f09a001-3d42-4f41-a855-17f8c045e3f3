"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Co<PERSON>, Check } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/lib/auth/AuthProvider";

const clientTabs = [
  { key: "claude", label: "<PERSON>", icon: "/images/claude.webp" },
  { key: "cursor", label: "Cursor", icon: "/images/cursor.png" },
  { key: "cline", label: "Cline", icon: "/images/cline.png" },
  { key: "roocline", label: "Roo Cline", icon: "/images/roocline.png" },
  { key: "trae", label: "Trae", icon: "/images/trae.png" },
  { key: "witsy", label: "<PERSON><PERSON><PERSON>", icon: "/images/witsy.png" },
];

const colorGradientMap: { [key: string]: string } = {
  claude:
    "data-[state=active]:bg-[linear-gradient(to_top,_rgba(239,108,60,0.3),_rgba(239,108,60,0))] data-[state=active]:border-[#EF6C3C]",
  cline:
    "data-[state=active]:bg-[linear-gradient(to_top,_rgba(112,128,144,0.3),_rgba(112,128,144,0))] data-[state=active]:border-[#708090]",
  cursor:
    "data-[state=active]:bg-[linear-gradient(to_top,_rgba(255,255,255,0.08),_rgba(255,255,255,0))] data-[state=active]:border-[#708090]",
  roocline:
    "data-[state=active]:bg-[linear-gradient(to_top,_rgba(45,32,92,0.8),_rgba(45,32,92,0))] data-[state=active]:border-[#7E3FF2]",
  trae:
    "data-[state=active]:bg-[linear-gradient(to_top,_rgba(0,176,137,0.3),_rgba(0,176,137,0))] data-[state=active]:border-[#00B089]",
  witsy:
    "data-[state=active]:bg-[linear-gradient(to_top,_rgba(33,135,255,0.3),_rgba(33,135,255,0))] data-[state=active]:border-[#2187FF]",
};

const getColorGradient = (color: string) => {
  if (colorGradientMap[color]) {
    return colorGradientMap[color];
  }
  return "data-[state=active]:bg-[linear-gradient(to_top,_rgba(126,63,242,0.3),_rgba(126,63,242,0))] data-[state=active]:border-[#7E3FF2]";
};

const allTabs = [{ key: "mcp", label: "MCP Link", icon: "🔗" }, ...clientTabs];

export const Install = () => {
  const [copiedTab, setCopiedTab] = useState<string | null>(null);
  const [copiedConfig, setCopiedConfig] = useState<string | null>(null);
  const { user, session, loading } = useAuth();
  
  const username = user?.email?.split('@')[0] || 'user';
  const URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8765";

  // Validate configuration before generating
  const isValidConfig = user && session && URL;

  // Generate dynamic MCP configuration
  const mcpConfig = {
    mcpServers: {
      'memory-master': {
        command: 'npx',
        args: ['-y', 'memory-master-mcp'],
        env: {
          MEMORY_MASTER_API: URL,
          MEMORY_MASTER_USER: username,
          SUPABASE_ACCESS_TOKEN: session?.access_token || 'NOT_AUTHENTICATED'
        }
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-zinc-400">Loading installation configuration...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-zinc-400">Please authenticate to view installation instructions.</div>
      </div>
    );
  }

  const handleCopy = async (tab: string, isMcp: boolean = false) => {
    const text = isMcp
      ? `${URL}/mcp/openmemory/sse/${username}`
      : `npx install-mcp i ${URL}/mcp/${tab}/sse/${username} --client ${tab}`;

    try {
      // Try using the Clipboard API first
      if (navigator?.clipboard?.writeText) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback: Create a temporary textarea element
        const textarea = document.createElement("textarea");
        textarea.value = text;
        textarea.style.position = "fixed";
        textarea.style.opacity = "0";
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand("copy");
        document.body.removeChild(textarea);
      }

      // Update UI to show success
      setCopiedTab(tab);
      setTimeout(() => setCopiedTab(null), 1500); // Reset after 1.5s
    } catch (error) {
      console.error("Failed to copy text:", error);
      // You might want to add a toast notification here to show the error
    }
  };

  const handleCopyConfig = async (configType: string) => {
    let text = '';
    
    if (configType === 'mcp-config') {
      text = JSON.stringify(mcpConfig, null, 2);
    } else if (configType === 'webhook-url') {
      text = `${URL}/webhook/${username}`;
    }

    try {
      if (navigator?.clipboard?.writeText) {
        await navigator.clipboard.writeText(text);
      } else {
        const textarea = document.createElement("textarea");
        textarea.value = text;
        textarea.style.position = "fixed";
        textarea.style.opacity = "0";
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand("copy");
        document.body.removeChild(textarea);
      }

      setCopiedConfig(configType);
      setTimeout(() => setCopiedConfig(null), 1500);
    } catch (error) {
      console.error("Failed to copy config:", error);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Install MemoryMaster</h2>

      <div className="hidden">
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(239,108,60,0.3),_rgba(239,108,60,0))] data-[state=active]:border-[#EF6C3C]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(112,128,144,0.3),_rgba(112,128,144,0))] data-[state=active]:border-[#708090]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(45,32,92,0.3),_rgba(45,32,92,0))] data-[state=active]:border-[#2D205C]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(0,176,137,0.3),_rgba(0,176,137,0))] data-[state=active]:border-[#00B089]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(33,135,255,0.3),_rgba(33,135,255,0))] data-[state=active]:border-[#2187FF]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(126,63,242,0.3),_rgba(126,63,242,0))] data-[state=active]:border-[#7E3FF2]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(239,108,60,0.3),_rgba(239,108,60,0))] data-[state=active]:border-[#EF6C3C]"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(107,33,168,0.3),_rgba(107,33,168,0))] data-[state=active]:border-primary"></div>
        <div className="data-[state=active]:bg-[linear-gradient(to_top,_rgba(255,255,255,0.08),_rgba(255,255,255,0))] data-[state=active]:border-[#708090]"></div>
      </div>

      <Tabs defaultValue="claude" className="w-full">
        <TabsList className="bg-transparent border-b border-zinc-800 rounded-none w-full justify-start gap-0 p-0 grid grid-cols-8">
          {allTabs.map(({ key, label, icon }) => (
            <TabsTrigger
              key={key}
              value={key}
              className={`flex-1 px-0 pb-2 rounded-none ${getColorGradient(
                key
              )} data-[state=active]:border-b-2 data-[state=active]:shadow-none text-zinc-400 data-[state=active]:text-white flex items-center justify-center gap-2 text-sm`}
            >
              {icon.startsWith("/") ? (
                <div>
                  <div className="w-6 h-6 rounded-full bg-zinc-700 flex items-center justify-center overflow-hidden">
                    <Image src={icon} alt={label} width={40} height={40} />
                  </div>
                </div>
              ) : (
                <div className="h-6">
                  <span className="relative top-1">{icon}</span>
                </div>
              )}
              <span>{label}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {/* MCP Tab Content */}
        <TabsContent value="mcp" className="mt-6">
          <div className="space-y-6">
            {/* MCP Direct Link */}
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader className="py-4">
                <CardTitle className="text-white text-xl">MCP Direct Link</CardTitle>
                <p className="text-sm text-zinc-400">Direct URL for MCP server connections</p>
              </CardHeader>
              <hr className="border-zinc-800" />
              <CardContent className="py-4">
                <div className="relative">
                  <pre className="bg-zinc-800 px-4 py-3 rounded-md overflow-x-auto text-sm">
                    <code className="text-gray-300">
                      {URL}/mcp/openmemory/sse/{username}
                    </code>
                  </pre>
                  <div>
                    <button
                      className="absolute top-0 right-0 py-3 px-4 rounded-md hover:bg-zinc-600 bg-zinc-700"
                      aria-label="Copy to clipboard"
                      onClick={() => handleCopy("mcp", true)}
                    >
                      {copiedTab === "mcp" ? (
                        <Check className="h-5 w-5 text-green-400" />
                      ) : (
                        <Copy className="h-5 w-5 text-zinc-400" />
                      )}
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* MCP Configuration */}
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader className="py-4">
                <CardTitle className="text-white text-xl">MCP Configuration</CardTitle>
                <p className="text-sm text-zinc-400">Add this to your MCP client configuration file</p>
              </CardHeader>
              <hr className="border-zinc-800" />
              <CardContent className="py-4">
                <div className="relative">
                  <pre className="bg-zinc-800 px-4 py-3 rounded-md overflow-x-auto text-sm max-h-64">
                    <code className="text-gray-300">
                      {JSON.stringify(mcpConfig, null, 2)}
                    </code>
                  </pre>
                  <div>
                    <button
                      className="absolute top-0 right-0 py-3 px-4 rounded-md hover:bg-zinc-600 bg-zinc-700"
                      aria-label="Copy configuration to clipboard"
                      onClick={() => handleCopyConfig("mcp-config")}
                    >
                      {copiedConfig === "mcp-config" ? (
                        <Check className="h-5 w-5 text-green-400" />
                      ) : (
                        <Copy className="h-5 w-5 text-zinc-400" />
                      )}
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Webhook URL */}
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader className="py-4">
                <CardTitle className="text-white text-xl">Webhook URL</CardTitle>
                <p className="text-sm text-zinc-400">Use this URL for webhook integrations</p>
              </CardHeader>
              <hr className="border-zinc-800" />
              <CardContent className="py-4">
                <div className="relative">
                  <pre className="bg-zinc-800 px-4 py-3 rounded-md overflow-x-auto text-sm">
                    <code className="text-gray-300">
                      {URL}/webhook/{username}
                    </code>
                  </pre>
                  <div>
                    <button
                      className="absolute top-0 right-0 py-3 px-4 rounded-md hover:bg-zinc-600 bg-zinc-700"
                      aria-label="Copy webhook URL to clipboard"
                      onClick={() => handleCopyConfig("webhook-url")}
                    >
                      {copiedConfig === "webhook-url" ? (
                        <Check className="h-5 w-5 text-green-400" />
                      ) : (
                        <Copy className="h-5 w-5 text-zinc-400" />
                      )}
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Installation Status */}
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader className="py-4">
                <CardTitle className="text-white text-xl">Installation Checklist</CardTitle>
                <p className="text-sm text-zinc-400">Follow these steps to complete your setup</p>
              </CardHeader>
              <hr className="border-zinc-800" />
              <CardContent className="py-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-sm text-gray-300">✓ User authenticated ({user?.email})</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${isValidConfig ? 'bg-green-400' : 'bg-red-400'}`}></div>
                    <span className="text-sm text-gray-300">
                      {isValidConfig ? '✓ Configuration valid' : '✗ Configuration incomplete'}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-sm text-gray-300">Copy MCP configuration to your client</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-sm text-gray-300">Test connection with your MCP client</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-sm text-gray-300">Verify memory operations work correctly</span>
                  </div>
                </div>
                
                {/* Configuration Details */}
                <div className="mt-4 pt-4 border-t border-zinc-800">
                  <h4 className="text-sm font-semibold text-white mb-2">Configuration Details</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-zinc-400">Username:</span>
                      <span className="text-gray-300">{username}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-zinc-400">API URL:</span>
                      <span className="text-gray-300">{URL}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-zinc-400">Session:</span>
                      <span className={`${session ? 'text-green-400' : 'text-red-400'}`}>
                        {session ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Troubleshooting */}
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader className="py-4">
                <CardTitle className="text-white text-xl">Troubleshooting</CardTitle>
                <p className="text-sm text-zinc-400">Common issues and solutions</p>
              </CardHeader>
              <hr className="border-zinc-800" />
              <CardContent className="py-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-semibold text-white mb-2">Connection Issues</h4>
                    <ul className="text-sm text-zinc-400 space-y-1">
                      <li>• Verify API URL is accessible from your client</li>
                      <li>• Check if authentication token is valid</li>
                      <li>• Ensure network connectivity to the server</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-white mb-2">Authentication Problems</h4>
                    <ul className="text-sm text-zinc-400 space-y-1">
                      <li>• Refresh your browser session</li>
                      <li>• Check if session has expired</li>
                      <li>• Verify your email is correctly configured</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-white mb-2">MCP Client Issues</h4>
                    <ul className="text-sm text-zinc-400 space-y-1">
                      <li>• Ensure MCP client supports the configuration format</li>
                      <li>• Check client-specific installation requirements</li>
                      <li>• Verify environment variables are set correctly</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Client Tabs Content */}
        {clientTabs.map(({ key }) => (
          <TabsContent key={key} value={key} className="mt-6">
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader className="py-4">
                <CardTitle className="text-white text-xl">
                  {key.charAt(0).toUpperCase() + key.slice(1)} Installation
                  Command
                </CardTitle>
              </CardHeader>
              <hr className="border-zinc-800" />
              <CardContent className="py-4">
                <div className="relative">
                  <pre className="bg-zinc-800 px-4 py-3 rounded-md overflow-x-auto text-sm">
                    <code className="text-gray-300">
                      {`npx install-mcp i ${URL}/mcp/${key}/sse/${username} --client ${key}`}
                    </code>
                  </pre>
                  <div>
                    <button
                      className="absolute top-0 right-0 py-3 px-4 rounded-md hover:bg-zinc-600 bg-zinc-700"
                      aria-label="Copy to clipboard"
                      onClick={() => handleCopy(key)}
                    >
                      {copiedTab === key ? (
                        <Check className="h-5 w-5 text-green-400" />
                      ) : (
                        <Copy className="h-5 w-5 text-zinc-400" />
                      )}
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default Install;
