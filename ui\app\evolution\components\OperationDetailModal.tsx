"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Search, Filter, Download, Clock, User, Brain, Target } from "lucide-react";
import { useEvolutionAnalytics, EvolutionAnalyticsItem } from "@/hooks/useEvolutionAnalytics";
import { cn } from "@/lib/utils";

interface OperationDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  operationType: 'ADD' | 'UPDATE' | 'DELETE' | 'NOOP';
  totalCount: number;
}

const operationConfig = {
  ADD: {
    color: 'bg-green-500',
    textColor: 'text-green-400',
    borderColor: 'border-green-500/20',
    icon: <Target className="h-4 w-4" />,
    description: 'New memory additions'
  },
  UPDATE: {
    color: 'bg-blue-500',
    textColor: 'text-blue-400',
    borderColor: 'border-blue-500/20',
    icon: <Brain className="h-4 w-4" />,
    description: 'Memory updates and enhancements'
  },
  DELETE: {
    color: 'bg-red-500',
    textColor: 'text-red-400',
    borderColor: 'border-red-500/20',
    icon: <Target className="h-4 w-4" />,
    description: 'Memory deletions and cleanup'
  },
  NOOP: {
    color: 'bg-yellow-500',
    textColor: 'text-yellow-400',
    borderColor: 'border-yellow-500/20',
    icon: <Clock className="h-4 w-4" />,
    description: 'No operation needed'
  }
};

export function OperationDetailModal({ isOpen, onClose, operationType, totalCount }: OperationDetailModalProps) {
  const [operations, setOperations] = useState<EvolutionAnalyticsItem[]>([]);
  const [filteredOperations, setFilteredOperations] = useState<EvolutionAnalyticsItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [confidenceFilter, setConfidenceFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [sortBy, setSortBy] = useState<'timestamp' | 'confidence'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  const { isLoading, fetchAnalytics, exportToCsv } = useEvolutionAnalytics();
  const config = operationConfig[operationType];

  useEffect(() => {
    if (isOpen) {
      loadOperations();
    }
  }, [isOpen, operationType]);

  useEffect(() => {
    filterAndSortOperations();
  }, [operations, searchTerm, confidenceFilter, sortBy, sortOrder]);

  const loadOperations = async () => {
    try {
      const response = await fetchAnalytics({
        operation_type: operationType,
        size: 100 // Get more detailed data
      });
      setOperations(response.items);
    } catch (error) {
      console.error('Failed to load operation details:', error);
    }
  };

  const filterAndSortOperations = () => {
    let filtered = [...operations];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(op => 
        op.reasoning?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        op.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply confidence filter
    if (confidenceFilter !== 'all') {
      filtered = filtered.filter(op => {
        const confidence = op.confidence_score;
        switch (confidenceFilter) {
          case 'high': return confidence >= 0.8;
          case 'medium': return confidence >= 0.5 && confidence < 0.8;
          case 'low': return confidence < 0.5;
          default: return true;
        }
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: number | string;
      let bValue: number | string;
      
      if (sortBy === 'timestamp') {
        aValue = new Date(a.timestamp).getTime();
        bValue = new Date(b.timestamp).getTime();
      } else {
        aValue = a.confidence_score;
        bValue = b.confidence_score;
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredOperations(filtered);
  };

  const handleExport = () => {
    exportToCsv(filteredOperations, `${operationType.toLowerCase()}-operations-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.5) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) return { variant: 'default' as const, label: 'High' };
    if (confidence >= 0.5) return { variant: 'secondary' as const, label: 'Medium' };
    return { variant: 'destructive' as const, label: 'Low' };
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] bg-zinc-900 border-zinc-700">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className={cn('p-2 rounded-lg', config.color)}>
              {config.icon}
            </div>
            <div>
              <span className="text-white">{operationType} Operations</span>
              <p className="text-sm text-zinc-400 font-normal mt-1">
                {config.description} • {totalCount} total operations
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        {/* Filters and Controls */}
        <div className="flex flex-wrap items-center gap-4 py-4 border-b border-zinc-700">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
              <Input
                placeholder="Search operations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-zinc-800 border-zinc-600"
              />
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-zinc-400" />
            <select
              value={confidenceFilter}
              onChange={(e) => setConfidenceFilter(e.target.value as any)}
              className="bg-zinc-800 border border-zinc-600 rounded px-3 py-1 text-sm text-white"
            >
              <option value="all">All Confidence</option>
              <option value="high">High (≥80%)</option>
              <option value="medium">Medium (50-79%)</option>
              <option value="low">Low (&lt;50%)</option>
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="bg-zinc-800 border border-zinc-600 rounded px-3 py-1 text-sm text-white"
            >
              <option value="timestamp-desc">Newest First</option>
              <option value="timestamp-asc">Oldest First</option>
              <option value="confidence-desc">Highest Confidence</option>
              <option value="confidence-asc">Lowest Confidence</option>
            </select>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={filteredOperations.length === 0}
            className="border-zinc-600 hover:bg-zinc-800"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>

        {/* Operations List */}
        <ScrollArea className="flex-1 max-h-[400px]">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-zinc-400">Loading operations...</div>
            </div>
          ) : filteredOperations.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-zinc-400">No operations found</div>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredOperations.map((operation) => {
                const { date, time } = formatTimestamp(operation.timestamp);
                const confidenceBadge = getConfidenceBadge(operation.confidence_score);
                
                return (
                  <div
                    key={operation.id}
                    className={cn(
                      'p-4 rounded-lg border bg-zinc-800/50',
                      config.borderColor
                    )}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Badge variant={confidenceBadge.variant} className="text-xs">
                          {confidenceBadge.label}
                        </Badge>
                        <span className={cn('text-sm font-medium', getConfidenceColor(operation.confidence_score))}>
                          {Math.round(operation.confidence_score * 100)}%
                        </span>
                        {operation.processing_time_ms && (
                          <span className="text-xs text-zinc-500">
                            {operation.processing_time_ms}ms
                          </span>
                        )}
                      </div>
                      <div className="text-right text-xs text-zinc-400">
                        <div>{date}</div>
                        <div>{time}</div>
                      </div>
                    </div>
                    
                    {operation.reasoning && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <p className="text-sm text-zinc-300 line-clamp-2 cursor-help">
                              {operation.reasoning}
                            </p>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-md">
                            <p>{operation.reasoning}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    
                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-zinc-700">
                      <div className="flex items-center gap-4 text-xs text-zinc-500">
                        <span>ID: {operation.id.slice(0, 8)}...</span>
                        {operation.similarity_score && (
                          <span>Similarity: {Math.round(operation.similarity_score * 100)}%</span>
                        )}
                      </div>
                      {operation.app_id && (
                        <div className="flex items-center gap-1 text-xs text-zinc-500">
                          <User className="h-3 w-3" />
                          <span>App: {operation.app_id.slice(0, 8)}...</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
        
        {/* Summary */}
        <div className="flex items-center justify-between pt-4 border-t border-zinc-700 text-sm text-zinc-400">
          <span>
            Showing {filteredOperations.length} of {operations.length} operations
          </span>
          <span>
            Total {operationType} operations: {totalCount}
          </span>
        </div>
      </DialogContent>
    </Dialog>
  );
}
