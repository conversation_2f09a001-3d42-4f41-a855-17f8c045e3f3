"""Test suite for evolution intelligence performance validation.

This module tests performance requirements for evolution intelligence,
including processing overhead, metrics queries, memory usage, and database operations.
"""

import pytest
import uuid
import time
import psutil
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import tempfile
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.database import Base
from app.models import User, App, Memory, EvolutionOperation, EvolutionInsight, EvolutionOperationType


class TestEvolutionPerformance:
    """Test evolution intelligence performance requirements."""
    
    @pytest.fixture(autouse=True)
    def setup_performance_test_environment(self):
        """Set up performance test environment with larger dataset."""
        # Create temporary database file
        self.db_fd, self.db_path = tempfile.mkstemp(suffix='.db')
        os.close(self.db_fd)
        
        try:
            # Create test engine with performance optimizations
            self.test_engine = create_engine(
                f'sqlite:///{self.db_path}',
                echo=False,
                pool_pre_ping=True,
                pool_recycle=300
            )
            Base.metadata.create_all(bind=self.test_engine)
            
            # Create session
            TestSession = sessionmaker(bind=self.test_engine)
            self.session = TestSession()
            
            # Create test entities
            self.test_user = User(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="Performance User"
            )
            self.session.add(self.test_user)
            
            self.test_app = App(
                id=uuid.uuid4(),
                name="Performance App",
                owner_id=self.test_user.id
            )
            self.session.add(self.test_app)
            
            # Create additional test apps for load testing
            self.test_apps = []
            for i in range(5):
                app = App(
                    id=uuid.uuid4(),
                    name=f"Load Test App {i}",
                    owner_id=self.test_user.id
                )
                self.test_apps.append(app)
                self.session.add(app)
            
            self.session.commit()
            
            # Track initial memory usage
            self.initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            yield
            
        finally:
            self.session.close()
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
    
    def test_evolution_processing_overhead_requirement(self):
        """Test that evolution processing overhead is <100ms per operation."""
        # Requirement: Evolution processing overhead <100ms per operation
        
        operations_to_test = 50
        processing_times = []
        
        for i in range(operations_to_test):
            start_time = time.perf_counter()
            
            # Simulate evolution operation processing
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact=f"Performance test fact {i}",
                confidence_score=0.85 + (i % 10) * 0.01,
                reasoning=f"Performance test reasoning {i}",
                similarity_score=0.80 + (i % 15) * 0.01,
                metadata={"test_id": i, "batch": "performance"}
            )
            
            self.session.add(operation)
            self.session.commit()
            
            end_time = time.perf_counter()
            processing_time = (end_time - start_time) * 1000  # Convert to milliseconds
            processing_times.append(processing_time)
        
        # Analyze performance
        avg_processing_time = sum(processing_times) / len(processing_times)
        max_processing_time = max(processing_times)
        p95_processing_time = sorted(processing_times)[int(0.95 * len(processing_times))]
        
        print(f"\nEvolution Processing Performance:")
        print(f"Average processing time: {avg_processing_time:.2f}ms")
        print(f"Maximum processing time: {max_processing_time:.2f}ms")
        print(f"95th percentile: {p95_processing_time:.2f}ms")
        
        # Assertions for performance requirements
        assert avg_processing_time < 100, f"Average processing time {avg_processing_time:.2f}ms exceeds 100ms requirement"
        assert p95_processing_time < 150, f"95th percentile {p95_processing_time:.2f}ms exceeds reasonable threshold"
        
        # Verify all operations were created successfully
        saved_operations = self.session.query(EvolutionOperation).filter_by(
            user_id=self.test_user.id
        ).count()
        assert saved_operations == operations_to_test
    
    def test_metrics_query_performance_requirement(self):
        """Test that metrics queries respond in <200ms."""
        # Requirement: Metrics queries <200ms response time
        
        # Create substantial dataset for realistic testing
        base_date = datetime.now().date() - timedelta(days=30)
        
        # Create operations across 30 days
        operations_per_day = 20
        total_operations = 30 * operations_per_day
        
        print(f"\nCreating {total_operations} operations for metrics testing...")
        
        operations = []
        for day in range(30):
            current_date = base_date + timedelta(days=day)
            for op_num in range(operations_per_day):
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=self.test_user.id,
                    app_id=self.test_app.id,
                    operation_type=EvolutionOperationType(((day + op_num) % 4) + 1),  # Cycle through types
                    candidate_fact=f"Metrics test fact {day}-{op_num}",
                    confidence_score=0.7 + (op_num % 20) * 0.015,
                    reasoning=f"Metrics test reasoning {day}-{op_num}",
                    similarity_score=0.6 + (op_num % 25) * 0.016,
                    created_at=datetime.combine(current_date, datetime.min.time()) + timedelta(hours=op_num % 24)
                )
                operations.append(operation)
        
        # Batch insert for better performance
        self.session.add_all(operations)
        self.session.commit()
        
        # Test various metrics queries
        metrics_queries = [
            # Daily operation counts
            lambda: self.session.query(EvolutionOperation).filter(
                EvolutionOperation.user_id == self.test_user.id,
                EvolutionOperation.created_at >= datetime.now() - timedelta(days=7)
            ).count(),
            
            # Operation type distribution
            lambda: self.session.query(
                EvolutionOperation.operation_type,
                self.session.query(EvolutionOperation).filter(
                    EvolutionOperation.user_id == self.test_user.id
                ).count()
            ).filter(
                EvolutionOperation.user_id == self.test_user.id
            ).group_by(EvolutionOperation.operation_type).all(),
            
            # Average confidence by date
            lambda: self.session.execute(text("""
                SELECT DATE(created_at) as date, AVG(confidence_score) as avg_confidence
                FROM evolution_operations 
                WHERE user_id = :user_id
                GROUP BY DATE(created_at)
                ORDER BY date DESC
                LIMIT 30
            """), {"user_id": str(self.test_user.id)}).fetchall(),
            
            # Recent high-confidence operations
            lambda: self.session.query(EvolutionOperation).filter(
                EvolutionOperation.user_id == self.test_user.id,
                EvolutionOperation.confidence_score > 0.9,
                EvolutionOperation.created_at >= datetime.now() - timedelta(days=14)
            ).limit(100).all()
        ]
        
        query_times = []
        
        for i, query_func in enumerate(metrics_queries):
            start_time = time.perf_counter()
            result = query_func()
            end_time = time.perf_counter()
            
            query_time = (end_time - start_time) * 1000  # Convert to milliseconds
            query_times.append(query_time)
            
            print(f"Query {i+1} time: {query_time:.2f}ms")
            
            # Verify query returned results
            if hasattr(result, '__len__'):
                assert len(result) > 0 or i == 1, f"Query {i+1} returned no results"
            else:
                assert result >= 0, f"Query {i+1} returned invalid result"
        
        # Performance assertions
        avg_query_time = sum(query_times) / len(query_times)
        max_query_time = max(query_times)
        
        print(f"\nMetrics Query Performance:")
        print(f"Average query time: {avg_query_time:.2f}ms")
        print(f"Maximum query time: {max_query_time:.2f}ms")
        
        assert avg_query_time < 200, f"Average query time {avg_query_time:.2f}ms exceeds 200ms requirement"
        assert max_query_time < 300, f"Maximum query time {max_query_time:.2f}ms exceeds reasonable threshold"
    
    def test_memory_usage_increase_requirement(self):
        """Test that memory usage increase is <10MB."""
        # Requirement: Memory usage increase <10MB
        
        # Create a substantial number of evolution operations
        operations_count = 1000
        
        print(f"\nTesting memory usage with {operations_count} operations...")
        print(f"Initial memory usage: {self.initial_memory:.2f}MB")
        
        # Create operations in batches to monitor memory growth
        batch_size = 100
        memory_measurements = [self.initial_memory]
        
        for batch in range(0, operations_count, batch_size):
            batch_operations = []
            
            for i in range(batch, min(batch + batch_size, operations_count)):
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=self.test_user.id,
                    app_id=self.test_app.id,
                    operation_type=EvolutionOperationType((i % 4) + 1),
                    candidate_fact=f"Memory test fact {i} with some additional content to simulate realistic data size",
                    confidence_score=0.75 + (i % 20) * 0.01,
                    reasoning=f"Memory test reasoning {i} with detailed explanation of the evolution decision process",
                    similarity_score=0.70 + (i % 25) * 0.012,
                    metadata={
                        "test_batch": batch // batch_size,
                        "operation_index": i,
                        "additional_data": f"Extra metadata for operation {i}" * 3
                    }
                )
                batch_operations.append(operation)
            
            # Add batch to database
            self.session.add_all(batch_operations)
            self.session.commit()
            
            # Measure memory usage
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_measurements.append(current_memory)
            
            print(f"After batch {batch // batch_size + 1}: {current_memory:.2f}MB")
        
        final_memory = memory_measurements[-1]
        memory_increase = final_memory - self.initial_memory
        
        print(f"\nMemory Usage Analysis:")
        print(f"Initial memory: {self.initial_memory:.2f}MB")
        print(f"Final memory: {final_memory:.2f}MB")
        print(f"Memory increase: {memory_increase:.2f}MB")
        
        # Performance assertion
        assert memory_increase < 10, f"Memory increase {memory_increase:.2f}MB exceeds 10MB requirement"
        
        # Verify all operations were created
        saved_operations = self.session.query(EvolutionOperation).filter_by(
            user_id=self.test_user.id
        ).count()
        assert saved_operations == operations_count
    
    def test_database_aggregation_performance_requirement(self):
        """Test that database aggregation operations complete in <50ms."""
        # Requirement: Database aggregation <50ms per operation
        
        # Create dataset for aggregation testing
        operations_count = 500
        insights_count = 30
        
        print(f"\nCreating {operations_count} operations and {insights_count} insights for aggregation testing...")
        
        # Create operations
        base_date = datetime.now() - timedelta(days=30)
        operations = []
        
        for i in range(operations_count):
            day_offset = i % 30
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                operation_type=EvolutionOperationType((i % 4) + 1),
                candidate_fact=f"Aggregation test fact {i}",
                confidence_score=0.6 + (i % 40) * 0.01,
                reasoning=f"Aggregation test reasoning {i}",
                similarity_score=0.5 + (i % 50) * 0.01,
                created_at=base_date + timedelta(days=day_offset, hours=i % 24)
            )
            operations.append(operation)
        
        self.session.add_all(operations)
        
        # Create insights
        insights = []
        for day in range(insights_count):
            insight_date = (base_date + timedelta(days=day)).date()
            insight = EvolutionInsight(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_app.id,
                date=insight_date,
                total_operations=15 + (day % 10),
                add_operations=5 + (day % 5),
                update_operations=4 + (day % 4),
                delete_operations=2 + (day % 3),
                noop_operations=4 + (day % 6),
                learning_efficiency=0.6 + (day % 20) * 0.02,
                conflict_resolution_count=day % 5,
                average_confidence=0.7 + (day % 25) * 0.012,
                average_similarity=0.65 + (day % 30) * 0.01
            )
            insights.append(insight)
        
        self.session.add_all(insights)
        self.session.commit()
        
        # Test various aggregation operations
        aggregation_operations = [
            # Sum total operations by user
            lambda: self.session.execute(text("""
                SELECT SUM(total_operations) as total
                FROM evolution_insights 
                WHERE user_id = :user_id
            """), {"user_id": str(self.test_user.id)}).scalar(),
            
            # Average learning efficiency by week
            lambda: self.session.execute(text("""
                SELECT AVG(learning_efficiency) as avg_efficiency
                FROM evolution_insights 
                WHERE user_id = :user_id
                AND date >= :start_date
            """), {
                "user_id": str(self.test_user.id),
                "start_date": (datetime.now() - timedelta(days=7)).date()
            }).scalar(),
            
            # Count operations by type in last 30 days
            lambda: self.session.execute(text("""
                SELECT operation_type, COUNT(*) as count
                FROM evolution_operations 
                WHERE user_id = :user_id
                AND created_at >= :start_date
                GROUP BY operation_type
            """), {
                "user_id": str(self.test_user.id),
                "start_date": datetime.now() - timedelta(days=30)
            }).fetchall(),
            
            # Calculate confidence trend
            lambda: self.session.execute(text("""
                SELECT DATE(created_at) as date, AVG(confidence_score) as avg_confidence
                FROM evolution_operations 
                WHERE user_id = :user_id
                AND created_at >= :start_date
                GROUP BY DATE(created_at)
                ORDER BY date
            """), {
                "user_id": str(self.test_user.id),
                "start_date": datetime.now() - timedelta(days=14)
            }).fetchall(),
            
            # Complex aggregation with joins
            lambda: self.session.execute(text("""
                SELECT 
                    ei.date,
                    ei.total_operations,
                    COUNT(eo.id) as actual_operations
                FROM evolution_insights ei
                LEFT JOIN evolution_operations eo ON 
                    ei.user_id = eo.user_id AND 
                    ei.app_id = eo.app_id AND
                    DATE(eo.created_at) = ei.date
                WHERE ei.user_id = :user_id
                GROUP BY ei.date, ei.total_operations
                ORDER BY ei.date DESC
                LIMIT 10
            """), {"user_id": str(self.test_user.id)}).fetchall()
        ]
        
        aggregation_times = []
        
        for i, agg_func in enumerate(aggregation_operations):
            start_time = time.perf_counter()
            result = agg_func()
            end_time = time.perf_counter()
            
            agg_time = (end_time - start_time) * 1000  # Convert to milliseconds
            aggregation_times.append(agg_time)
            
            print(f"Aggregation {i+1} time: {agg_time:.2f}ms")
            
            # Verify aggregation returned valid results
            if hasattr(result, '__len__'):
                assert len(result) >= 0, f"Aggregation {i+1} failed"
            else:
                assert result is not None, f"Aggregation {i+1} returned None"
        
        # Performance analysis
        avg_aggregation_time = sum(aggregation_times) / len(aggregation_times)
        max_aggregation_time = max(aggregation_times)
        
        print(f"\nDatabase Aggregation Performance:")
        print(f"Average aggregation time: {avg_aggregation_time:.2f}ms")
        print(f"Maximum aggregation time: {max_aggregation_time:.2f}ms")
        
        # Performance assertions
        assert avg_aggregation_time < 50, f"Average aggregation time {avg_aggregation_time:.2f}ms exceeds 50ms requirement"
        assert max_aggregation_time < 100, f"Maximum aggregation time {max_aggregation_time:.2f}ms exceeds reasonable threshold"
    
    def test_concurrent_evolution_performance(self):
        """Test performance under concurrent evolution operations."""
        # Test concurrent performance to ensure no significant degradation
        
        concurrent_users = 5
        operations_per_user = 20
        
        def create_user_operations(user_index):
            """Create operations for a specific user."""
            # Create separate session for each thread
            TestSession = sessionmaker(bind=self.test_engine)
            thread_session = TestSession()
            
            try:
                # Create user for this thread
                thread_user = User(
                    id=uuid.uuid4(),
                    email=f"concurrent_user_{user_index}@example.com",
                    name=f"Concurrent User {user_index}"
                )
                thread_session.add(thread_user)
                
                thread_app = App(
                    id=uuid.uuid4(),
                    name=f"Concurrent App {user_index}",
                    owner_id=thread_user.id
                )
                thread_session.add(thread_app)
                thread_session.commit()
                
                operation_times = []
                
                for op_index in range(operations_per_user):
                    start_time = time.perf_counter()
                    
                    operation = EvolutionOperation(
                        id=uuid.uuid4(),
                        user_id=thread_user.id,
                        app_id=thread_app.id,
                        operation_type=EvolutionOperationType((op_index % 4) + 1),
                        candidate_fact=f"Concurrent fact {user_index}-{op_index}",
                        confidence_score=0.7 + (op_index % 20) * 0.015,
                        reasoning=f"Concurrent reasoning {user_index}-{op_index}",
                        similarity_score=0.65 + (op_index % 25) * 0.014
                    )
                    
                    thread_session.add(operation)
                    thread_session.commit()
                    
                    end_time = time.perf_counter()
                    operation_time = (end_time - start_time) * 1000
                    operation_times.append(operation_time)
                
                return {
                    "user_index": user_index,
                    "operation_times": operation_times,
                    "avg_time": sum(operation_times) / len(operation_times),
                    "max_time": max(operation_times)
                }
                
            finally:
                thread_session.close()
        
        print(f"\nTesting concurrent performance with {concurrent_users} users...")
        
        # Execute concurrent operations
        start_time = time.perf_counter()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(create_user_operations, i) for i in range(concurrent_users)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.perf_counter()
        total_time = (end_time - start_time) * 1000
        
        # Analyze concurrent performance
        all_operation_times = []
        for result in results:
            all_operation_times.extend(result["operation_times"])
        
        avg_concurrent_time = sum(all_operation_times) / len(all_operation_times)
        max_concurrent_time = max(all_operation_times)
        
        print(f"\nConcurrent Performance Results:")
        print(f"Total execution time: {total_time:.2f}ms")
        print(f"Average operation time: {avg_concurrent_time:.2f}ms")
        print(f"Maximum operation time: {max_concurrent_time:.2f}ms")
        print(f"Total operations: {len(all_operation_times)}")
        
        # Performance assertions for concurrent operations
        assert avg_concurrent_time < 150, f"Average concurrent operation time {avg_concurrent_time:.2f}ms exceeds threshold"
        assert max_concurrent_time < 300, f"Maximum concurrent operation time {max_concurrent_time:.2f}ms exceeds threshold"
        
        # Verify all operations were created
        total_operations = self.session.query(EvolutionOperation).count()
        expected_operations = concurrent_users * operations_per_user
        assert total_operations >= expected_operations, f"Expected {expected_operations} operations, got {total_operations}"
    
    def test_large_dataset_performance(self):
        """Test performance with large datasets to ensure scalability."""
        # Test with larger dataset to validate scalability
        
        large_dataset_size = 2000
        print(f"\nTesting performance with large dataset ({large_dataset_size} operations)...")
        
        # Create large dataset in batches
        batch_size = 200
        creation_times = []
        
        for batch_start in range(0, large_dataset_size, batch_size):
            batch_end = min(batch_start + batch_size, large_dataset_size)
            
            start_time = time.perf_counter()
            
            batch_operations = []
            for i in range(batch_start, batch_end):
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=self.test_user.id,
                    app_id=self.test_apps[i % len(self.test_apps)].id,
                    operation_type=EvolutionOperationType((i % 4) + 1),
                    candidate_fact=f"Large dataset fact {i}",
                    confidence_score=0.5 + (i % 50) * 0.01,
                    reasoning=f"Large dataset reasoning {i}",
                    similarity_score=0.4 + (i % 60) * 0.01,
                    created_at=datetime.now() - timedelta(days=i % 90, hours=i % 24)
                )
                batch_operations.append(operation)
            
            self.session.add_all(batch_operations)
            self.session.commit()
            
            end_time = time.perf_counter()
            batch_time = (end_time - start_time) * 1000
            creation_times.append(batch_time)
            
            print(f"Batch {batch_start//batch_size + 1}: {batch_time:.2f}ms")
        
        # Test query performance on large dataset
        query_start = time.perf_counter()
        
        # Complex query on large dataset
        result = self.session.execute(text("""
            SELECT 
                operation_type,
                COUNT(*) as count,
                AVG(confidence_score) as avg_confidence,
                AVG(similarity_score) as avg_similarity
            FROM evolution_operations 
            WHERE user_id = :user_id
            AND created_at >= :start_date
            GROUP BY operation_type
            ORDER BY count DESC
        """), {
            "user_id": str(self.test_user.id),
            "start_date": datetime.now() - timedelta(days=30)
        }).fetchall()
        
        query_end = time.perf_counter()
        query_time = (query_end - query_start) * 1000
        
        print(f"\nLarge Dataset Performance:")
        print(f"Average batch creation time: {sum(creation_times)/len(creation_times):.2f}ms")
        print(f"Complex query time on {large_dataset_size} records: {query_time:.2f}ms")
        print(f"Query returned {len(result)} result rows")
        
        # Performance assertions for large datasets
        avg_batch_time = sum(creation_times) / len(creation_times)
        assert avg_batch_time < 500, f"Average batch creation time {avg_batch_time:.2f}ms exceeds threshold"
        assert query_time < 300, f"Complex query time {query_time:.2f}ms exceeds threshold"
        
        # Verify data integrity
        total_count = self.session.query(EvolutionOperation).filter_by(
            user_id=self.test_user.id
        ).count()
        assert total_count >= large_dataset_size, f"Expected at least {large_dataset_size} operations, got {total_count}"
    
    def test_mcp_evolution_tools_performance(self):
        """Test performance of MCP evolution tools integration."""
        from unittest.mock import Mock, patch
        
        # Mock MCP server responses for performance testing
        mock_mcp_responses = {
            "get_evolution_metrics": {
                "total_operations": 150,
                "learning_efficiency": 0.85,
                "avg_confidence": 0.82,
                "processing_time_ms": 25
            },
            "analyze_conversation_patterns": {
                "technical_depth": 0.9,
                "complexity_score": 0.8,
                "domain_expertise": 0.85,
                "processing_time_ms": 45
            },
            "generate_custom_prompt": {
                "prompt_template": "Technical conversation prompt for {domain}",
                "effectiveness_score": 0.92,
                "processing_time_ms": 35
            }
        }
        
        mcp_call_times = []
        
        # Test each MCP tool performance
        for tool_name, mock_response in mock_mcp_responses.items():
            with patch('app.mcp_server.call_mcp_tool') as mock_mcp:
                mock_mcp.return_value = mock_response
                
                start_time = time.perf_counter()
                
                # Simulate MCP tool call
                result = mock_mcp(tool_name, {
                    "user_id": str(self.test_user.id),
                    "app_id": str(self.test_app.id)
                })
                
                end_time = time.perf_counter()
                call_time = (end_time - start_time) * 1000
                mcp_call_times.append(call_time)
                
                print(f"MCP tool '{tool_name}': {call_time:.2f}ms")
                
                # Verify response
                assert result is not None
                assert 'processing_time_ms' in result
                
                # Performance assertion for individual MCP calls
                assert call_time < 100, f"MCP tool '{tool_name}' took {call_time:.2f}ms, exceeds 100ms limit"
        
        # Overall MCP performance analysis
        avg_mcp_time = sum(mcp_call_times) / len(mcp_call_times)
        max_mcp_time = max(mcp_call_times)
        
        print(f"\nMCP Tools Performance:")
        print(f"Average call time: {avg_mcp_time:.2f}ms")
        print(f"Maximum call time: {max_mcp_time:.2f}ms")
        
        assert avg_mcp_time < 75, f"Average MCP call time {avg_mcp_time:.2f}ms exceeds 75ms target"
        assert max_mcp_time < 150, f"Maximum MCP call time {max_mcp_time:.2f}ms exceeds 150ms limit"
    
    def test_evolution_service_batch_performance(self):
        """Test EvolutionService batch processing performance."""
        from unittest.mock import Mock
        
        # Mock EvolutionService for performance testing
        class MockEvolutionService:
            def __init__(self, session):
                self.session = session
            
            def process_evolution_batch(self, operations):
                """Mock batch processing with realistic timing."""
                start_time = time.perf_counter()
                
                # Simulate processing overhead
                processed_operations = []
                for op in operations:
                    # Simulate individual operation processing
                    time.sleep(0.001)  # 1ms per operation
                    processed_operations.append({
                        "id": str(uuid.uuid4()),
                        "type": op.get("type", "ADD"),
                        "confidence": op.get("confidence", 0.8),
                        "processed": True
                    })
                
                end_time = time.perf_counter()
                processing_time = (end_time - start_time) * 1000
                
                return {
                    "processed_count": len(processed_operations),
                    "processing_time_ms": processing_time,
                    "operations": processed_operations
                }
        
        evolution_service = MockEvolutionService(self.session)
        
        # Test different batch sizes
        batch_sizes = [10, 25, 50, 100]
        batch_performance = []
        
        for batch_size in batch_sizes:
            # Create test batch
            test_batch = []
            for i in range(batch_size):
                test_batch.append({
                    "type": ["ADD", "UPDATE", "DELETE", "NOOP"][i % 4],
                    "fact": f"Batch test fact {i}",
                    "confidence": 0.7 + (i % 30) / 100,
                    "user_id": str(self.test_user.id)
                })
            
            # Process batch and measure performance
            start_time = time.perf_counter()
            result = evolution_service.process_evolution_batch(test_batch)
            end_time = time.perf_counter()
            
            total_time = (end_time - start_time) * 1000
            per_operation_time = total_time / batch_size
            
            batch_performance.append({
                "batch_size": batch_size,
                "total_time_ms": total_time,
                "per_operation_ms": per_operation_time,
                "processed_count": result["processed_count"]
            })
            
            print(f"Batch size {batch_size}: {total_time:.2f}ms total, {per_operation_time:.2f}ms per operation")
            
            # Performance assertions
            assert per_operation_time < 50, f"Per-operation time {per_operation_time:.2f}ms exceeds 50ms limit"
            assert result["processed_count"] == batch_size, f"Expected {batch_size} processed, got {result['processed_count']}"
        
        # Analyze batch scaling performance
        avg_per_operation = sum(bp["per_operation_ms"] for bp in batch_performance) / len(batch_performance)
        
        print(f"\nBatch Processing Performance:")
        print(f"Average per-operation time: {avg_per_operation:.2f}ms")
        
        assert avg_per_operation < 30, f"Average per-operation time {avg_per_operation:.2f}ms exceeds 30ms target"
    
    def test_database_index_performance(self):
        """Test database index performance for evolution queries."""
        # Create additional data to test index effectiveness
        index_test_operations = 1000
        
        print(f"\nCreating {index_test_operations} operations for index performance testing...")
        
        # Create operations with varied data for index testing
        operations = []
        base_date = datetime.now() - timedelta(days=60)
        
        for i in range(index_test_operations):
            operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=self.test_user.id,
                app_id=self.test_apps[i % len(self.test_apps)].id,
                operation_type=EvolutionOperationType((i % 4) + 1),
                candidate_fact=f"Index test fact {i}",
                confidence_score=0.3 + (i % 70) * 0.01,
                reasoning=f"Index test reasoning {i}",
                similarity_score=0.2 + (i % 80) * 0.01,
                created_at=base_date + timedelta(days=i % 60, hours=i % 24, minutes=i % 60)
            )
            operations.append(operation)
        
        # Batch insert
        self.session.add_all(operations)
        self.session.commit()
        
        # Test queries that should benefit from indexes
        index_queries = [
            # User ID index
            {
                "name": "user_id_filter",
                "query": "SELECT COUNT(*) FROM evolution_operations WHERE user_id = :user_id",
                "params": {"user_id": str(self.test_user.id)}
            },
            # App ID index
            {
                "name": "app_id_filter",
                "query": "SELECT COUNT(*) FROM evolution_operations WHERE app_id = :app_id",
                "params": {"app_id": str(self.test_app.id)}
            },
            # Date range index
            {
                "name": "date_range_filter",
                "query": "SELECT COUNT(*) FROM evolution_operations WHERE created_at >= :start_date AND created_at <= :end_date",
                "params": {
                    "start_date": datetime.now() - timedelta(days=7),
                    "end_date": datetime.now()
                }
            },
            # Operation type index
            {
                "name": "operation_type_filter",
                "query": "SELECT COUNT(*) FROM evolution_operations WHERE operation_type = :op_type",
                "params": {"op_type": EvolutionOperationType.ADD.value}
            },
            # Composite index query
            {
                "name": "composite_filter",
                "query": "SELECT COUNT(*) FROM evolution_operations WHERE user_id = :user_id AND operation_type = :op_type AND created_at >= :start_date",
                "params": {
                    "user_id": str(self.test_user.id),
                    "op_type": EvolutionOperationType.ADD.value,
                    "start_date": datetime.now() - timedelta(days=30)
                }
            }
        ]
        
        index_query_times = []
        
        for query_info in index_queries:
            start_time = time.perf_counter()
            
            result = self.session.execute(text(query_info["query"]), query_info["params"])
            count = result.scalar()
            
            end_time = time.perf_counter()
            query_time = (end_time - start_time) * 1000
            index_query_times.append(query_time)
            
            print(f"Index query '{query_info['name']}': {query_time:.2f}ms ({count} results)")
            
            # Performance assertion for indexed queries
            assert query_time < 100, f"Indexed query '{query_info['name']}' took {query_time:.2f}ms, exceeds 100ms limit"
        
        # Overall index performance analysis
        avg_index_time = sum(index_query_times) / len(index_query_times)
        max_index_time = max(index_query_times)
        
        print(f"\nDatabase Index Performance:")
        print(f"Average indexed query time: {avg_index_time:.2f}ms")
        print(f"Maximum indexed query time: {max_index_time:.2f}ms")
        
        assert avg_index_time < 75, f"Average indexed query time {avg_index_time:.2f}ms exceeds 75ms target"
        assert max_index_time < 150, f"Maximum indexed query time {max_index_time:.2f}ms exceeds 150ms limit"
    
    def test_performance_regression_monitoring(self):
        """Test performance regression monitoring and alerting."""
        # Define performance baselines based on requirements
        performance_baselines = {
            "evolution_processing_ms": 100,
            "metrics_query_ms": 200,
            "memory_increase_mb": 10,
            "database_aggregation_ms": 50,
            "mcp_tool_call_ms": 100,
            "batch_processing_per_op_ms": 50
        }
        
        # Simulate current performance measurements
        current_performance = {
            "evolution_processing_ms": 85,  # Good performance
            "metrics_query_ms": 180,       # Good performance
            "memory_increase_mb": 8.5,     # Good performance
            "database_aggregation_ms": 45,  # Good performance
            "mcp_tool_call_ms": 75,        # Good performance
            "batch_processing_per_op_ms": 35  # Good performance
        }
        
        # Performance analysis
        performance_status = {}
        regressions = []
        improvements = []
        
        for metric, baseline in performance_baselines.items():
            current = current_performance.get(metric, baseline)
            
            # Calculate performance ratio
            ratio = current / baseline
            
            if ratio > 1.1:  # 10% regression threshold
                status = "REGRESSION"
                regressions.append(f"{metric}: {current:.2f} vs {baseline} (+{((ratio-1)*100):.1f}%)")
            elif ratio < 0.9:  # 10% improvement
                status = "IMPROVEMENT"
                improvements.append(f"{metric}: {current:.2f} vs {baseline} ({((ratio-1)*100):.1f}%)")
            else:
                status = "STABLE"
            
            performance_status[metric] = {
                "current": current,
                "baseline": baseline,
                "ratio": ratio,
                "status": status
            }
        
        # Generate performance report
        print(f"\nPerformance Regression Analysis:")
        print(f"Baselines vs Current Performance:")
        
        for metric, status in performance_status.items():
            print(f"  {metric}: {status['current']:.2f} (baseline: {status['baseline']}) [{status['status']}]")
        
        if regressions:
            print(f"\nRegressions detected:")
            for regression in regressions:
                print(f"  - {regression}")
        
        if improvements:
            print(f"\nImprovements detected:")
            for improvement in improvements:
                print(f"  - {improvement}")
        
        if not regressions and not improvements:
            print(f"\nAll metrics within acceptable ranges (±10%)")
        
        # Assert no critical regressions
        critical_metrics = ["evolution_processing_ms", "memory_increase_mb"]
        critical_regressions = [r for r in regressions if any(cm in r for cm in critical_metrics)]
        
        assert len(critical_regressions) == 0, f"Critical performance regressions detected: {critical_regressions}"
        
        # Verify performance monitoring is working
        assert len(performance_status) == len(performance_baselines), "Performance monitoring incomplete"
        
        # Calculate overall performance score
        performance_scores = [1/status['ratio'] if status['ratio'] > 1 else status['ratio'] for status in performance_status.values()]
        overall_score = sum(performance_scores) / len(performance_scores)
        
        print(f"\nOverall Performance Score: {overall_score:.3f} (1.0 = perfect baseline match)")
        
        assert overall_score > 0.8, f"Overall performance score {overall_score:.3f} below acceptable threshold"