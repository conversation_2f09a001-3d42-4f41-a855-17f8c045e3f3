"""
Test suite for chunked memory operation consistency.

This module tests the transaction-like behavior for chunked operations added in Task 10,
including atomic operations, rollback capabilities, and chunk verification.
"""

import unittest
import uuid
import time
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.mcp_server import MemoryTransaction
from app.models import Memory, MemoryState


class TestMemoryTransaction(unittest.TestCase):
    """Test MemoryTransaction functionality."""
    
    def setUp(self):
        """Set up test environment before each test."""
        self.mock_client = Mock()
        self.user_id = "test-user"
        self.client_name = "test-client"
        self.transaction = MemoryTransaction(self.mock_client, self.user_id, self.client_name)
    
    def tearDown(self):
        """Clean up after each test."""
        pass
    
    def test_transaction_initialization(self):
        """Test transaction initialization."""
        self.assertEqual(self.transaction.user_id, self.user_id)
        self.assertEqual(self.transaction.client_name, self.client_name)
        self.assertEqual(len(self.transaction.operations), 0)
        self.assertEqual(len(self.transaction.results), 0)
        self.assertFalse(self.transaction.committed)
        self.assertIsNotNone(self.transaction.transaction_id)
    
    def test_add_memory_chunk(self):
        """Test adding memory chunks to transaction."""
        content1 = "First chunk content"
        metadata1 = {"test": "metadata1"}
        
        success = self.transaction.add_memory_chunk(content1, metadata1)
        self.assertTrue(success)
        self.assertEqual(len(self.transaction.operations), 1)
        
        # Check that transaction metadata was added
        operation = self.transaction.operations[0]
        self.assertEqual(operation['content'], content1)
        self.assertEqual(operation['metadata']['transaction_id'], self.transaction.transaction_id)
        self.assertEqual(operation['metadata']['chunk_index'], 0)
        self.assertEqual(operation['metadata']['test'], "metadata1")
        self.assertTrue(operation['metadata']['is_chunk'])
        
        # Add second chunk
        content2 = "Second chunk content"
        success = self.transaction.add_memory_chunk(content2)
        self.assertTrue(success)
        self.assertEqual(len(self.transaction.operations), 2)
        
        # Check chunk index incremented
        operation2 = self.transaction.operations[1]
        self.assertEqual(operation2['metadata']['chunk_index'], 1)
    
    def test_add_chunk_to_committed_transaction(self):
        """Test that adding chunks to committed transaction fails."""
        self.transaction.committed = True
        
        with self.assertRaises(ValueError) as context:
            self.transaction.add_memory_chunk("test content")
        
        self.assertIn("committed transaction", str(context.exception))
    
    @patch('app.mcp_server.add_memory_with_retry')
    @patch('app.mcp_server.validate_mem0_response')
    def test_successful_commit(self, mock_validate, mock_add_memory):
        """Test successful transaction commit."""
        # Setup mocks
        mock_add_memory.return_value = {
            "results": [{"id": str(uuid.uuid4()), "event": "ADD", "memory": "test content"}]
        }
        mock_validate.return_value = (True, "Success")
        
        # Add chunks to transaction
        self.transaction.add_memory_chunk("Chunk 1", {"test": "data1"})
        self.transaction.add_memory_chunk("Chunk 2", {"test": "data2"})
        
        # Mock chunk verification
        with patch.object(self.transaction, '_verify_chunks', return_value=True):
            success, message, results = self.transaction.commit()
        
        self.assertTrue(success)
        self.assertIn("committed successfully", message)
        self.assertEqual(len(results), 2)
        self.assertTrue(self.transaction.committed)
        
        # Verify add_memory_with_retry was called for each chunk
        self.assertEqual(mock_add_memory.call_count, 2)
        self.assertEqual(mock_validate.call_count, 2)
    
    @patch('app.mcp_server.add_memory_with_retry')
    @patch('app.mcp_server.validate_mem0_response')
    def test_commit_with_validation_failure(self, mock_validate, mock_add_memory):
        """Test transaction commit with validation failure."""
        # Setup mocks - first succeeds, second fails validation
        mock_add_memory.side_effect = [
            {"results": [{"id": str(uuid.uuid4()), "event": "ADD"}]},
            {"results": [{"id": str(uuid.uuid4()), "event": "ADD"}]}
        ]
        mock_validate.side_effect = [(True, "Success"), (False, "Validation failed")]
        
        # Add chunks
        self.transaction.add_memory_chunk("Chunk 1")
        self.transaction.add_memory_chunk("Chunk 2")
        
        # Mock rollback
        with patch.object(self.transaction, 'rollback', return_value=(True, "Rollback successful")):
            success, message, results = self.transaction.commit()
        
        self.assertFalse(success)
        self.assertIn("rollback succeeded", message)
        self.assertEqual(len(results), 0)
        self.assertFalse(self.transaction.committed)
    
    @patch('app.mcp_server.add_memory_with_retry')
    def test_commit_with_exception(self, mock_add_memory):
        """Test transaction commit with exception."""
        # Setup mock to raise exception
        mock_add_memory.side_effect = Exception("Memory operation failed")
        
        # Add chunk
        self.transaction.add_memory_chunk("Test chunk")
        
        # Mock rollback
        with patch.object(self.transaction, 'rollback', return_value=(True, "Rollback successful")):
            success, message, results = self.transaction.commit()
        
        self.assertFalse(success)
        self.assertIn("exception", message)
        self.assertFalse(self.transaction.committed)
    
    @patch('app.mcp_server.SessionLocal')
    def test_rollback_functionality(self, mock_session_local):
        """Test transaction rollback functionality."""
        # Setup mock database
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        mock_memory = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_memory
        
        # Add some results to rollback
        memory_id = str(uuid.uuid4())
        self.transaction.results = [
            {"results": [{"id": memory_id, "event": "ADD"}]}
        ]
        
        success, message = self.transaction.rollback()
        
        self.assertTrue(success)
        self.assertIn("successful", message)
        self.assertEqual(len(self.transaction.results), 0)
        
        # Verify memory was marked as deleted
        self.assertEqual(mock_memory.state, MemoryState.deleted.value)
        mock_db.commit.assert_called_once()
    
    @patch('app.mcp_server.SessionLocal')
    @patch('app.mcp_server.search_memories_with_retry')
    def test_chunk_verification(self, mock_search, mock_session_local):
        """Test chunk verification functionality."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        
        mock_memory = Mock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_memory
        
        memory_id = str(uuid.uuid4())
        mock_search.return_value = [{"id": memory_id, "content": "test"}]
        
        # Add results to verify
        self.transaction.results = [
            {"results": [{"id": memory_id, "event": "ADD", "memory": "test content"}]}
        ]
        
        verification_result = self.transaction._verify_chunks()
        
        self.assertTrue(verification_result)
        mock_db.query.assert_called()
        mock_search.assert_called_once()
    
    def test_get_status(self):
        """Test transaction status reporting."""
        # Add some operations
        self.transaction.add_memory_chunk("Chunk 1")
        self.transaction.add_memory_chunk("Chunk 2")
        
        status = self.transaction.get_status()
        
        self.assertEqual(status['transaction_id'], self.transaction.transaction_id)
        self.assertEqual(status['user_id'], self.user_id)
        self.assertEqual(status['client_name'], self.client_name)
        self.assertEqual(status['operations_count'], 2)
        self.assertEqual(status['results_count'], 0)
        self.assertFalse(status['committed'])
        self.assertIn('start_time', status)
        self.assertIn('duration_seconds', status)
    
    def test_empty_transaction_commit(self):
        """Test committing empty transaction."""
        success, message, results = self.transaction.commit()
        
        self.assertTrue(success)
        self.assertIn("No operations", message)
        self.assertEqual(len(results), 0)
    
    def test_double_commit_prevention(self):
        """Test that double commit is prevented."""
        self.transaction.committed = True
        
        with self.assertRaises(ValueError) as context:
            self.transaction.commit()
        
        self.assertIn("already committed", str(context.exception))


class TestChunkedOperationIntegration(unittest.TestCase):
    """Integration tests for chunked operations."""
    
    def setUp(self):
        """Set up integration test environment."""
        pass
    
    def tearDown(self):
        """Clean up integration test environment."""
        pass
    
    @patch('app.mcp_server.get_memory_client_safe')
    @patch('app.mcp_server.MemoryTransaction')
    def test_chunked_operation_with_transaction(self, mock_transaction_class, mock_get_client):
        """Test that chunked operations use transactions."""
        # This would be an integration test that verifies the chunked operation
        # logic in add_memories uses MemoryTransaction
        
        mock_client = Mock()
        mock_get_client.return_value = mock_client
        
        mock_transaction = Mock()
        mock_transaction.commit.return_value = (True, "Success", [{"id": "test"}])
        mock_transaction.transaction_id = "test-transaction"
        mock_transaction_class.return_value = mock_transaction
        
        # This test would need to be expanded based on the actual integration
        # with the add_memories function
        pass
    
    def test_concurrent_transactions(self):
        """Test multiple concurrent transactions."""
        import threading
        import queue
        
        results = queue.Queue()
        errors = queue.Queue()
        
        def create_transaction(thread_id):
            try:
                mock_client = Mock()
                transaction = MemoryTransaction(mock_client, f"user-{thread_id}", "test-client")
                
                # Add some chunks
                for i in range(3):
                    transaction.add_memory_chunk(f"Thread {thread_id} chunk {i}")
                
                results.put(transaction.get_status())
            except Exception as e:
                errors.put((thread_id, str(e)))
        
        # Create multiple concurrent transactions
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_transaction, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check results
        self.assertEqual(errors.qsize(), 0, "No errors should occur")
        self.assertEqual(results.qsize(), 5, "All transactions should be created")
        
        # Verify each transaction has unique ID
        transaction_ids = set()
        while not results.empty():
            status = results.get()
            self.assertNotIn(status['transaction_id'], transaction_ids)
            transaction_ids.add(status['transaction_id'])
            self.assertEqual(status['operations_count'], 3)
    
    def test_transaction_isolation(self):
        """Test that transactions are isolated from each other."""
        mock_client = Mock()
        
        transaction1 = MemoryTransaction(mock_client, "user1", "client1")
        transaction2 = MemoryTransaction(mock_client, "user2", "client2")
        
        # Add different chunks to each transaction
        transaction1.add_memory_chunk("Transaction 1 content")
        transaction2.add_memory_chunk("Transaction 2 content")
        transaction2.add_memory_chunk("Transaction 2 second chunk")
        
        # Verify isolation
        self.assertNotEqual(transaction1.transaction_id, transaction2.transaction_id)
        self.assertEqual(len(transaction1.operations), 1)
        self.assertEqual(len(transaction2.operations), 2)
        self.assertEqual(transaction1.operations[0]['content'], "Transaction 1 content")
        self.assertEqual(transaction2.operations[0]['content'], "Transaction 2 content")


if __name__ == '__main__':
    unittest.main()
