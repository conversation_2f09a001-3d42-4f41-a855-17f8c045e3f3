# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Memory Master v2 is a comprehensive personal memory layer for LLMs that provides private, portable, and open-source memory management. It consists of:

- **Backend API (FastAPI)**: RESTful API and MCP server functionality in Python
- **Frontend UI (Next.js)**: React-based dashboard with TypeScript
- **Vector Store (Qdrant)**: High-performance vector database for embeddings
- **Database (Supabase/PostgreSQL)**: Relational data storage for metadata

## Common Development Commands

### Docker Operations
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f memory-mcp  # API logs
docker-compose logs -f memory-ui   # UI logs

# Restart services
docker restart memory-mcp
docker-compose restart

# Check service status
docker-compose ps
```

### Backend Development (API)
```bash
cd api

# Local development setup
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt

# Run locally
uvicorn main:app --reload --host 0.0.0.0 --port 8765

# Run tests
pytest tests/ -v --cov=app
pytest tests/test_evolution_*.py -v  # Evolution intelligence tests

# Database migrations
alembic upgrade head      # Apply migrations
alembic revision -m "description"  # Create new migration
```

### Frontend Development (UI)
```bash
cd ui

# Install dependencies (uses pnpm)
pnpm install

# Development server
pnpm dev

# Build for production
pnpm build

# Run linting
pnpm lint

# Start production server
pnpm start
```

### Testing Commands
```bash
# Backend unit tests
cd api && pytest tests/test_memory_service.py -v

# Integration tests
cd api && pytest tests/test_comprehensive_integration.py -v

# Evolution intelligence tests
cd api && pytest tests/test_evolution_basic.py -v

# Frontend tests
cd ui && pnpm test
```

## High-Level Architecture

### Memory Processing Flow
1. **Input Reception**: API receives memory text via REST or MCP endpoints
2. **Text Processing**: Automatic chunking for large texts (>2000 words)
3. **Vector Generation**: OpenAI embeddings via text-embedding-3-small
4. **Evolution Intelligence**: Determines ADD/UPDATE/DELETE/NOOP operations
5. **Storage**: Vectors in Qdrant, metadata in PostgreSQL
6. **Retrieval**: Semantic search using vector similarity

### Evolution Intelligence System
The system uses custom prompts to intelligently manage memories:
- **ADD**: New information storage
- **UPDATE**: Enhance existing memories
- **DELETE**: Remove outdated information
- **NOOP**: Skip redundant data

**Critical**: DELETE/NOOP operations require technical domain configuration with custom prompts loaded.

### Authentication Architecture
- **Legacy Mode**: Hardcoded USER environment variable
- **Authenticated Mode**: Supabase Auth integration
- **Migration Path**: Scripts in `/scripts` for user data migration

### Key Service Dependencies
```
UI (3000) → API (8765) → Qdrant (6333)
                      ↘ PostgreSQL (5432)
```

## Critical Configuration Points

### Evolution Intelligence Activation
```bash
# Switch to technical domain for DELETE/NOOP operations
curl -X POST "http://localhost:8765/api/v1/evolution-config/domain/switch" \
  -H "Content-Type: application/json" \
  -d '{"domain_type": "technical"}'
```

### Environment Variables
**API (.env)**:
- `OPENAI_API_KEY`: Required for embeddings and LLM operations
- `DATABASE_URL`: PostgreSQL connection string
- `USER`: User identifier (legacy mode)
- `MIGRATION_MODE`: Set to "supabase_only" for external DB

**UI (.env)**:
- `NEXT_PUBLIC_API_URL`: Backend API URL (default: http://localhost:8765)
- `NEXT_PUBLIC_USER_ID`: Must match API USER value
- `NEXT_PUBLIC_AUTH_ENABLED`: Toggle authentication features

### Database Migrations
Always create migrations for schema changes:
```bash
cd api
alembic revision -m "Add new_column to memories"
# Edit generated file in alembic/versions/
alembic upgrade head
```

## Key Implementation Patterns

### Memory Service Pattern
```python
# api/app/memory_service.py
class MemoryService:
    def add_memory(self, text: str, user_id: str, client_name: str) -> Tuple[bool, str, Optional[str]]
    # Returns: (success, message, memory_id)
```

### Evolution Tracking Pattern
```python
# api/app/services/evolution_service.py
def _track_evolution_operations(self, mem0_response, user_id, app_id, original_text):
    # Priority: Check 'memory' array first (custom prompts)
    # Fallback: Check 'results' array (standard format)
```

### Frontend State Management
```typescript
// ui/store/memoriesSlice.ts
// Uses Redux Toolkit for state management
// Key slices: memories, apps, auth, config, ui
```

## Common Issues and Solutions

### DELETE/NOOP Operations Not Working
1. Check Evolution Intelligence status: `docker logs memory-mcp | grep "Evolution Intelligence"`
2. Verify technical domain active: `curl http://localhost:8765/api/v1/evolution-config/domain/current`
3. Restart container after domain switch: `docker restart memory-mcp`

### Memory Not Stored
- Check OpenAI API key is valid
- Verify Qdrant is running: `curl http://localhost:6333/health`
- Check logs for mem0 filtering: `docker logs memory-mcp | grep "not memorable"`

### UI Authentication Issues
- Ensure `NEXT_PUBLIC_USER_ID` matches API `USER` value
- Check Supabase configuration if auth enabled
- Verify API CORS settings allow frontend origin

## Testing Strategies

### Evolution Operations Testing
```python
# Test DELETE operation
add_memory("I use MySQL for databases")
add_memory("I switched from MySQL to PostgreSQL")  # Should trigger DELETE + ADD

# Test NOOP operation
add_memory("I prefer React for UI")
add_memory("I prefer React for UI")  # Should trigger NOOP
```

### Integration Testing
- Use `docker-compose -f docker-compose.test.yml` for isolated test environment
- Mock external services (OpenAI, Supabase) for unit tests
- Use real services for integration tests

## Performance Considerations

- **Response Time Target**: <200ms for memory operations
- **Concurrent Workers**: API runs with 4 workers by default
- **Memory Limits**: Qdrant limited to 2GB, API to 1GB
- **Text Chunking**: Automatic for texts >2000 words
- **Vector Dimensions**: 1536 (OpenAI text-embedding-3-small)

## Security Best Practices

- Never commit `.env` files or API keys
- Use environment variables for all secrets
- Implement rate limiting for public deployments
- Validate all user inputs before processing
- Use prepared statements for all SQL queries