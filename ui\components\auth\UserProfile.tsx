'use client'

import React, { useState } from 'react'
import { useAuth } from '@/lib/auth/AuthProvider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { LogOut, Mail, Calendar, Shield, Loader2 } from 'lucide-react'
import { format } from 'date-fns'

export function UserProfile() {
  const { user, signOut } = useAuth()
  const [loading, setLoading] = useState(false)

  if (!user) {
    return null
  }

  const handleSignOut = async () => {
    setLoading(true)
    try {
      const { error } = await signOut()
      if (error) {
        toast.error('Failed to sign out')
      } else {
        toast.success('Signed out successfully')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const getInitials = (email: string) => {
    return email.charAt(0).toUpperCase()
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP')
    } catch {
      return 'Unknown'
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Avatar className="h-20 w-20">
            <AvatarImage src={user.user_metadata?.avatar_url} />
            <AvatarFallback className="text-lg">
              {getInitials(user.email || '')}
            </AvatarFallback>
          </Avatar>
        </div>
        <CardTitle>{user.user_metadata?.full_name || 'User'}</CardTitle>
        <CardDescription>Account Information</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Email */}
        <div className="flex items-center gap-2">
          <Mail className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{user.email}</span>
          {user.email_confirmed_at && (
            <Badge variant="secondary" className="text-xs">
              Verified
            </Badge>
          )}
        </div>

        {/* Account Created */}
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            Joined {formatDate(user.created_at)}
          </span>
        </div>

        {/* Account Provider */}
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm capitalize">
            {user.app_metadata?.provider || 'email'} account
          </span>
        </div>

        {/* Last Sign In */}
        {user.last_sign_in_at && (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              Last sign in: {formatDate(user.last_sign_in_at)}
            </span>
          </div>
        )}

        <Separator />

        {/* User ID for debugging */}
        <div className="text-xs text-muted-foreground font-mono">
          ID: {user.id}
        </div>

        <Button 
          onClick={handleSignOut}
          variant="outline"
          className="w-full"
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Signing out...
            </>
          ) : (
            <>
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}