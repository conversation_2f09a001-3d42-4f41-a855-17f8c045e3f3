import { createClient } from './supabase'

export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export interface AuthenticatedRequestOptions extends RequestInit {
  requireAuth?: boolean
  retryCount?: number
}

export class ApiError extends Error {
  status: number
  response?: Response

  constructor(message: string, status: number, response?: Response) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.response = response
  }
}

/**
 * Authenticated fetch wrapper that automatically includes Supabase auth headers
 */
export async function authenticatedFetch(
  url: string, 
  options: AuthenticatedRequestOptions = {}
): Promise<Response> {
  const { 
    requireAuth = true, 
    retryCount = 0,
    headers: customHeaders = {},
    ...fetchOptions 
  } = options

  // Check if authentication is enabled
  const isAuthEnabled = process.env.NEXT_PUBLIC_AUTH_ENABLED === 'true'
  
  // Base headers
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...customHeaders
  }

  // Add authentication headers if auth is enabled and required
  if (isAuthEnabled && requireAuth) {
    const supabase = createClient()
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('Error getting session:', error)
      throw new ApiError('Authentication error', 401)
    }
    
    if (!session) {
      throw new ApiError('Not authenticated', 401)
    }
    
    headers['Authorization'] = `Bearer ${session.access_token}`
  }

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers
    })

    // Handle 401 responses (token expired or invalid)
    if (response.status === 401 && isAuthEnabled && requireAuth) {
      // Try to refresh the session
      const supabase = createClient()
      const { data, error: refreshError } = await supabase.auth.refreshSession()
      
      if (refreshError || !data.session) {
        // Refresh failed, redirect to login
        console.error('Session refresh failed:', refreshError)
        
        // Only redirect on client side
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login'
        }
        
        throw new ApiError('Session expired', 401, response)
      }
      
      // Retry the request with the new token
      if (retryCount < 1) {
        return authenticatedFetch(url, {
          ...options,
          retryCount: retryCount + 1,
          headers: {
            ...customHeaders,
            'Authorization': `Bearer ${data.session.access_token}`
          }
        })
      }
    }

    // Handle other HTTP errors
    if (!response.ok) {
      const errorText = await response.text()
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`
      
      try {
        const errorData = JSON.parse(errorText)
        errorMessage = errorData.message || errorData.error || errorMessage
      } catch {
        // Use status text if response is not JSON
      }
      
      throw new ApiError(errorMessage, response.status, response)
    }

    return response
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    
    // Network or other errors
    console.error('API request failed:', error)
    throw new ApiError(
      error instanceof Error ? error.message : 'Network error',
      0
    )
  }
}

/**
 * Convenience method for GET requests
 */
export async function apiGet<T = any>(
  url: string, 
  options: AuthenticatedRequestOptions = {}
): Promise<T> {
  const response = await authenticatedFetch(url, {
    ...options,
    method: 'GET'
  })
  
  return response.json()
}

/**
 * Convenience method for POST requests
 */
export async function apiPost<T = any>(
  url: string, 
  data?: any, 
  options: AuthenticatedRequestOptions = {}
): Promise<T> {
  const response = await authenticatedFetch(url, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined
  })
  
  return response.json()
}

/**
 * Convenience method for PUT requests
 */
export async function apiPut<T = any>(
  url: string, 
  data?: any, 
  options: AuthenticatedRequestOptions = {}
): Promise<T> {
  const response = await authenticatedFetch(url, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined
  })
  
  return response.json()
}

/**
 * Convenience method for DELETE requests
 */
export async function apiDelete<T = any>(
  url: string, 
  options: AuthenticatedRequestOptions = {}
): Promise<T> {
  const response = await authenticatedFetch(url, {
    ...options,
    method: 'DELETE'
  })
  
  // Handle empty responses
  const contentLength = response.headers.get('content-length')
  if (contentLength === '0' || response.status === 204) {
    return {} as T
  }
  
  return response.json()
}

/**
 * Get the base API URL
 */
export function getApiUrl(): string {
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8765'
}

/**
 * Build full API endpoint URL
 */
export function buildApiUrl(endpoint: string): string {
  const baseUrl = getApiUrl()
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseUrl}${cleanEndpoint}`
}