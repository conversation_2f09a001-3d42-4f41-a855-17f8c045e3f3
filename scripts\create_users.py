#!/usr/bin/env python3
"""
User Management Script for Memory Master

This script creates <PERSON><PERSON> and <PERSON><PERSON>'s accounts in Supabase with proper permissions.
It uses the Supabase Admin API to create users programmatically.

Usage:
    python scripts/create_users.py

Environment Variables Required:
    SUPABASE_URL - Your Supabase project URL
    SUPABASE_SERVICE_ROLE_KEY - Your Supabase service role key (not anon key)
"""

import os
import sys
import logging
from typing import List, Dict, Any
import uuid
from datetime import datetime

try:
    from supabase import create_client, Client
    from supabase.client import AuthAPIError
except ImportError:
    print("Error: Supabase library not installed. Run: pip install supabase")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UserManager:
    def __init__(self):
        """Initialize the UserManager with Supabase client."""
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not self.supabase_url or not self.service_key:
            raise ValueError(
                "Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY"
            )
        
        self.supabase: Client = create_client(self.supabase_url, self.service_key)
        logger.info("✅ Supabase client initialized successfully")

    def create_user(self, email: str, password: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new user in Supabase Auth.
        
        Args:
            email: User's email address
            password: User's password
            metadata: Optional user metadata
            
        Returns:
            User creation result
        """
        try:
            user_data = {
                'email': email,
                'password': password,
                'email_confirm': True,  # Skip email verification for admin-created users
                'user_metadata': metadata or {}
            }
            
            logger.info(f"Creating user: {email}")
            result = self.supabase.auth.admin.create_user(user_data)
            
            if result.user:
                logger.info(f"✅ Successfully created user: {email} (ID: {result.user.id})")
                return {
                    'success': True,
                    'user': result.user,
                    'email': email
                }
            else:
                logger.error(f"❌ Failed to create user: {email} - No user object returned")
                return {'success': False, 'email': email, 'error': 'No user object returned'}
                
        except AuthAPIError as e:
            logger.error(f"❌ Auth API error creating user {email}: {e}")
            return {'success': False, 'email': email, 'error': str(e)}
        except Exception as e:
            logger.error(f"❌ Unexpected error creating user {email}: {e}")
            return {'success': False, 'email': email, 'error': str(e)}

    def list_users(self) -> List[Dict[str, Any]]:
        """List all users in Supabase Auth."""
        try:
            logger.info("Fetching existing users...")
            result = self.supabase.auth.admin.list_users()
            
            if hasattr(result, 'users'):
                users = result.users
                logger.info(f"Found {len(users)} existing users")
                return users
            else:
                logger.warning("No users found or invalid response")
                return []
                
        except Exception as e:
            logger.error(f"Error fetching users: {e}")
            return []

    def user_exists(self, email: str) -> bool:
        """Check if a user with the given email already exists."""
        users = self.list_users()
        for user in users:
            if user.email == email:
                return True
        return False

    def create_project_users(self) -> Dict[str, Any]:
        """Create the specific users for this project."""
        
        # Define the users to create
        users_to_create = [
            {
                'email': '<EMAIL>',
                'password': 'MemoryMaster2024!Aung',
                'metadata': {
                    'full_name': 'Aung Hein Aye',
                    'role': 'admin',
                    'created_by': 'admin_script',
                    'project': 'memory-master'
                }
            },
            {
                'email': '<EMAIL>', 
                'password': 'MemoryMaster2024!Yohanna',
                'metadata': {
                    'full_name': 'Yohanna',
                    'role': 'admin',
                    'created_by': 'admin_script',
                    'project': 'memory-master'
                }
            }
        ]
        
        results = {
            'created': [],
            'skipped': [],
            'errors': []
        }
        
        logger.info("🚀 Starting user creation process...")
        
        for user_data in users_to_create:
            email = user_data['email']
            
            # Check if user already exists
            if self.user_exists(email):
                logger.info(f"⏭️  User {email} already exists, skipping...")
                results['skipped'].append(email)
                continue
            
            # Create the user
            result = self.create_user(
                email=email,
                password=user_data['password'],
                metadata=user_data['metadata']
            )
            
            if result['success']:
                results['created'].append(result)
                logger.info(f"🎉 User {email} created successfully!")
            else:
                results['errors'].append(result)
                logger.error(f"💥 Failed to create user {email}: {result.get('error', 'Unknown error')}")
        
        return results

    def print_summary(self, results: Dict[str, Any]):
        """Print a summary of the user creation process."""
        print("\n" + "="*60)
        print("USER CREATION SUMMARY")
        print("="*60)
        
        print(f"\n✅ Created: {len(results['created'])} users")
        for user_result in results['created']:
            user = user_result['user']
            print(f"   • {user.email} (ID: {user.id})")
        
        if results['skipped']:
            print(f"\n⏭️  Skipped: {len(results['skipped'])} users (already exist)")
            for email in results['skipped']:
                print(f"   • {email}")
        
        if results['errors']:
            print(f"\n❌ Errors: {len(results['errors'])} users")
            for error_result in results['errors']:
                print(f"   • {error_result['email']}: {error_result['error']}")
        
        print(f"\n📊 Total processed: {len(results['created']) + len(results['skipped']) + len(results['errors'])}")
        
        if results['created']:
            print("\n🔐 IMPORTANT SECURITY NOTES:")
            print("1. Users have been created with temporary passwords")
            print("2. Users should change their passwords after first login")
            print("3. Consider setting up password policies in Supabase dashboard")
            print("4. Monitor user activity through Supabase Auth logs")
        
        print("\n" + "="*60)

def main():
    """Main function to run the user creation script."""
    try:
        print("🚀 Memory Master User Creation Script")
        print("=====================================")
        
        # Initialize user manager
        user_manager = UserManager()
        
        # Create users
        results = user_manager.create_project_users()
        
        # Print summary
        user_manager.print_summary(results)
        
        # Exit with appropriate code
        if results['errors']:
            print("\n⚠️  Script completed with errors. Check the logs above.")
            sys.exit(1)
        else:
            print("\n🎉 Script completed successfully!")
            sys.exit(0)
            
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        print("\n💡 Make sure to set the required environment variables:")
        print("   export SUPABASE_URL='your-supabase-url'")
        print("   export SUPABASE_SERVICE_ROLE_KEY='your-service-role-key'")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()