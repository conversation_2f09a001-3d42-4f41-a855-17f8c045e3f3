import json
import logging

from openai import OpenAI
from typing import List
from dotenv import load_dotenv
from pydantic import BaseModel
from tenacity import retry, stop_after_attempt, wait_exponential
from app.utils.prompts import MEMORY_CATEGORIZATION_PROMPT

load_dotenv()

openai_client = OpenAI()


class MemoryCategories(BaseModel):
    categories: List[str]


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=15))
def get_categories_for_memory(memory: str) -> List[str]:
    """Get categories for a memory."""
    try:
        response = openai_client.responses.parse(
            model="gpt-4o-mini",
            instructions=MEMORY_CATEGORIZATION_PROMPT,
            input=memory,
            temperature=0,
            text_format=MemoryCategories,
        )

        # Enhanced JSON parsing with error handling
        try:
            response_text = response.output[0].content[0].text
            logging.debug(f"OpenAI response text: {response_text}")

            # Clean the response text to handle potential formatting issues
            response_text = response_text.strip()

            # Try to extract JSON if it's wrapped in markdown code blocks
            if response_text.startswith('```json'):
                response_text = response_text.replace('```json', '').replace('```', '').strip()
            elif response_text.startswith('```'):
                response_text = response_text.replace('```', '').strip()

            response_json = json.loads(response_text)
            categories = response_json['categories']
            categories = [cat.strip().lower() for cat in categories]
            # TODO: Validate categories later may be
            return categories

        except json.JSONDecodeError as json_error:
            logging.error(f"JSON parsing error in categorization: {json_error}")
            logging.error(f"Raw response text: {response_text}")
            # Return default categories on JSON parsing failure
            return ["general", "uncategorized"]

    except Exception as e:
        logging.error(f"Error in get_categories_for_memory: {e}")
        # Return default categories on any error
        return ["general", "uncategorized"]
