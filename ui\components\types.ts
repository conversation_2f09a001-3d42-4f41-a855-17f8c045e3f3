export type Category = "personal" | "work" | "health" | "finance" | "travel" | "education" | "preferences" | "relationships"
export type Client = "chrome" | "chatgpt" | "cursor" | "trae" | "terminal" | "api"

export interface CategoryObject {
  id: string;
  name: string;
  description: string;
  updated_at: string;
  created_at: string;
}

export interface Memory {
  id: string
  memory: string
  metadata: any
  client: Client
  categories: CategoryObject[]
  created_at: number
  app_name: string
  state: "active" | "paused" | "archived" | "deleted"
}