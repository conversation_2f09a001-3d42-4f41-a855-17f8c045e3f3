from datetime import datetime
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

class MemoryBase(BaseModel):
    content: str
    metadata_: Optional[dict] = Field(default_factory=dict)

class MemoryCreate(MemoryBase):
    user_id: UUID
    app_id: UUID


class Category(BaseModel):
    name: str


class App(BaseModel):
    id: UUID
    name: str


class Memory(MemoryBase):
    id: UUID
    user_id: UUID
    app_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    state: str
    categories: Optional[List[Category]] = None
    app: App

    class Config:
        from_attributes = True

class MemoryUpdate(BaseModel):
    content: Optional[str] = None
    metadata_: Optional[dict] = None
    state: Optional[str] = None


class MemoryResponse(BaseModel):
    id: UUID
    content: str
    created_at: int
    state: str
    app_id: UUID
    app_name: str
    categories: List[str]
    metadata_: Optional[dict] = None

    @validator('created_at', pre=True)
    def convert_to_epoch(cls, v):
        if isinstance(v, datetime):
            return int(v.timestamp())
        return v

class PaginatedMemoryResponse(BaseModel):
    items: List[MemoryResponse]
    total: int
    page: int
    size: int
    pages: int


# Evolution Configuration Schemas
class DomainTypeEnum(str, Enum):
    technical_development = "technical_development"
    business_operations = "business_operations"


class EvolutionOperationTypeEnum(str, Enum):
    ADD = "ADD"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    NOOP = "NOOP"


class NoopThresholdBase(BaseModel):
    similarity_threshold: float = Field(default=0.95, ge=0.0, le=1.0)
    update_threshold: float = Field(default=0.85, ge=0.0, le=1.0)
    content_length_min: int = Field(default=10, ge=1)
    confidence_threshold: float = Field(default=0.8, ge=0.0, le=1.0)


class NoopThresholdCreate(NoopThresholdBase):
    pass


class NoopThresholdUpdate(BaseModel):
    similarity_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)
    update_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)
    content_length_min: Optional[int] = Field(None, ge=1)
    confidence_threshold: Optional[float] = Field(None, ge=0.0, le=1.0)


class NoopThreshold(NoopThresholdBase):
    id: UUID
    config_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EvolutionConfigurationBase(BaseModel):
    domain_type: DomainTypeEnum
    fact_extraction_prompt: str = Field(..., min_length=1)
    memory_evolution_prompt: str = Field(..., min_length=1)
    is_active: bool = True
    metadata_: Optional[dict] = Field(default_factory=dict)


class EvolutionConfigurationCreate(EvolutionConfigurationBase):
    user_id: UUID
    app_id: Optional[UUID] = None
    noop_threshold: Optional[NoopThresholdCreate] = None


class EvolutionConfigurationUpdate(BaseModel):
    domain_type: Optional[DomainTypeEnum] = None
    fact_extraction_prompt: Optional[str] = Field(None, min_length=1)
    memory_evolution_prompt: Optional[str] = Field(None, min_length=1)
    is_active: Optional[bool] = None
    metadata_: Optional[dict] = None
    noop_threshold: Optional[NoopThresholdUpdate] = None


class EvolutionConfiguration(EvolutionConfigurationBase):
    id: UUID
    user_id: UUID
    app_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    noop_threshold: Optional[NoopThreshold] = None

    class Config:
        from_attributes = True


class ConfigurationVersionBase(BaseModel):
    changes: dict
    rollback_data: Optional[dict] = None


class ConfigurationVersionCreate(ConfigurationVersionBase):
    config_id: UUID
    created_by: UUID


class ConfigurationVersion(ConfigurationVersionBase):
    id: UUID
    config_id: UUID
    version_number: int
    created_by: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class EvolutionAnalyticsBase(BaseModel):
    operation_type: EvolutionOperationTypeEnum
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    similarity_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    reasoning: Optional[str] = None
    processing_time_ms: Optional[int] = Field(None, ge=0)
    metadata_: Optional[dict] = Field(default_factory=dict)
    candidate_fact: Optional[str] = None
    existing_memory_content: Optional[str] = None


class EvolutionAnalyticsCreate(EvolutionAnalyticsBase):
    user_id: UUID
    app_id: Optional[UUID] = None
    config_id: Optional[UUID] = None


class EvolutionAnalytics(EvolutionAnalyticsBase):
    id: UUID
    user_id: UUID
    app_id: Optional[UUID]
    config_id: Optional[UUID]
    timestamp: datetime

    class Config:
        from_attributes = True


class PaginatedEvolutionConfigurationResponse(BaseModel):
    items: List[EvolutionConfiguration]
    total: int
    page: int
    size: int
    pages: int


class PaginatedEvolutionAnalyticsResponse(BaseModel):
    items: List[EvolutionAnalytics]
    total: int
    page: int
    size: int
    pages: int
