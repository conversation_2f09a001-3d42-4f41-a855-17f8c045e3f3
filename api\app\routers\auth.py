from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User
from app.auth.middleware import get_current_user, require_authentication, AuthenticatedUser, DefaultUser
from app.auth.supabase import supabase_client
from app.middleware.security import auth_security
from pydantic import BaseModel
from typing import Union, Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])

class UserResponse(BaseModel):
    user_id: str
    email: str
    name: Optional[str] = None
    email_verified: bool = False
    is_authenticated: bool = False
    supabase_user_id: Optional[str] = None
    created_at: Optional[str] = None
    last_sign_in_at: Optional[str] = None

    class Config:
        from_attributes = True

class AuthStatusResponse(BaseModel):
    auth_enabled: bool
    is_authenticated: bool
    user: Optional[UserResponse] = None

@router.get("/status", response_model=AuthStatusResponse)
async def get_auth_status(
    current_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current authentication status and user information.
    """
    try:
        # Get user from database
        user_data = None
        if current_user.is_authenticated:
            db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
            if db_user:
                user_data = UserResponse(
                    user_id=db_user.user_id,
                    email=db_user.email or "",
                    name=db_user.name,
                    email_verified=db_user.email_verified or False,
                    is_authenticated=True,
                    supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
                    created_at=db_user.created_at.isoformat() if db_user.created_at else None,
                    last_sign_in_at=db_user.last_sign_in_at.isoformat() if db_user.last_sign_in_at else None
                )
        else:
            # For default user
            user_data = UserResponse(
                user_id=current_user.user_id,
                email=current_user.email,
                name="Default User",
                email_verified=False,
                is_authenticated=False
            )

        return AuthStatusResponse(
            auth_enabled=supabase_client.auth_enabled,
            is_authenticated=current_user.is_authenticated,
            user=user_data
        )
    except Exception as e:
        logger.error(f"Error getting auth status: {e}")
        return AuthStatusResponse(
            auth_enabled=supabase_client.auth_enabled,
            is_authenticated=False,
            user=None
        )

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: AuthenticatedUser = Depends(require_authentication),
    db: Session = Depends(get_db)
):
    """
    Get the current user's profile. Requires authentication.
    """
    try:
        db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
        
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        return UserResponse(
            user_id=db_user.user_id,
            email=db_user.email or "",
            name=db_user.name,
            email_verified=db_user.email_verified or False,
            is_authenticated=True,
            supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
            created_at=db_user.created_at.isoformat() if db_user.created_at else None,
            last_sign_in_at=db_user.last_sign_in_at.isoformat() if db_user.last_sign_in_at else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

class UpdateProfileRequest(BaseModel):
    name: Optional[str] = None

@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    request: UpdateProfileRequest,
    current_user: AuthenticatedUser = Depends(require_authentication),
    db: Session = Depends(get_db)
):
    """
    Update the current user's profile. Requires authentication.
    """
    try:
        db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
        
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # Update fields
        if request.name is not None:
            db_user.name = request.name

        db.commit()
        db.refresh(db_user)

        return UserResponse(
            user_id=db_user.user_id,
            email=db_user.email or "",
            name=db_user.name,
            email_verified=db_user.email_verified or False,
            is_authenticated=True,
            supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
            created_at=db_user.created_at.isoformat() if db_user.created_at else None,
            last_sign_in_at=db_user.last_sign_in_at.isoformat() if db_user.last_sign_in_at else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/logout")
async def logout(
    request: Request,
    current_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user)
):
    """
    Comprehensive logout endpoint that invalidates the session.
    """
    from datetime import datetime
    try:
        # Log the logout attempt for security monitoring
        if current_user.is_authenticated:
            logger.info(f"Logout initiated for user: {current_user.email}")
            
            # Verify the user is authenticated
            if not current_user.supabase_user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No active session found"
                )
            
            # Sign out from Supabase (this invalidates the JWT token)
            result = supabase_client.client.auth.sign_out()
            
            if result.get('error'):
                logger.error(f"Supabase logout error for {current_user.email}: {result['error']}")
                # Continue with logout process even if Supabase fails
            
            logger.info(f"Logout completed for user: {current_user.email}")
            
            return {
                "message": "Successfully logged out",
                "user_id": current_user.user_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            # Default user or already logged out
            return {
                "message": "No active session to logout",
                "timestamp": datetime.utcnow().isoformat()
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during logout"
        )

class LoginAttemptRequest(BaseModel):
    email: str
    password: str

@router.post("/validate-login")
async def validate_login_attempt(
    request: Request,
    login_data: LoginAttemptRequest
):
    """
    Pre-login security validation endpoint.
    Checks for rate limiting and brute force protection before actual login.
    This should be called by the frontend before attempting Supabase login.
    """
    try:
        # Check brute force protection (this will raise HTTPException if blocked)
        # protection_status = await auth_security.check_brute_force(request, login_data.email)  # Disabled for internal tool
        
        return {
            "allowed": True,
            "ip_remaining_attempts": 999,  # High number to indicate no limit
            "user_remaining_attempts": 999,  # High number to indicate no limit
            "message": "Login attempt allowed - security checks disabled for internal tool"
        }
        
    except HTTPException as e:
        # Re-raise HTTP exceptions (rate limiting, brute force protection)
        raise e
    except Exception as e:
        logger.error(f"Error validating login attempt: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

class LoginSuccessRequest(BaseModel):
    email: str

@router.post("/login-success")
async def record_login_success(
    request: Request,
    login_data: LoginSuccessRequest,
    current_user: AuthenticatedUser = Depends(require_authentication)
):
    """
    Records a successful login to reset brute force protection counters.
    This should be called by the frontend after successful Supabase login.
    """
    from datetime import datetime
    try:
        # Record successful authentication
        auth_security.record_successful_auth(request, login_data.email)
        
        logger.info(f"Successful login recorded for: {login_data.email}")
        
        return {
            "message": "Login success recorded",
            "user_id": current_user.user_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error recording login success: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )