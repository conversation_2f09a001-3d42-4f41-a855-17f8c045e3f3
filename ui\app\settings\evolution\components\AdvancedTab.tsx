"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Settings, 
  Zap, 
  Database, 
  Clock, 
  Shield,
  AlertTriangle,
  Info,
  Cpu,
  HardDrive,
  Network
} from "lucide-react";

interface AdvancedSettings {
  performance: {
    batchSize: number;
    maxConcurrentOperations: number;
    timeoutSeconds: number;
    retryAttempts: number;
    cacheEnabled: boolean;
    cacheTtlMinutes: number;
  };
  memory: {
    vectorDimensions: number;
    similarityThreshold: number;
    maxMemoryAge: number;
    compressionEnabled: boolean;
    deduplicationEnabled: boolean;
  };
  security: {
    encryptionEnabled: boolean;
    auditLogging: boolean;
    accessControlEnabled: boolean;
    rateLimitEnabled: boolean;
    maxRequestsPerMinute: number;
  };
  monitoring: {
    metricsEnabled: boolean;
    detailedLogging: boolean;
    performanceTracking: boolean;
    alertsEnabled: boolean;
    healthCheckInterval: number;
  };
}

interface AdvancedTabProps {
  onSettingsChange: (hasChanges: boolean) => void;
}

export function AdvancedTab({ onSettingsChange }: AdvancedTabProps) {
  const [settings, setSettings] = useState<AdvancedSettings>({
    performance: {
      batchSize: 50,
      maxConcurrentOperations: 10,
      timeoutSeconds: 30,
      retryAttempts: 3,
      cacheEnabled: true,
      cacheTtlMinutes: 60
    },
    memory: {
      vectorDimensions: 1536,
      similarityThreshold: 0.8,
      maxMemoryAge: 365,
      compressionEnabled: false,
      deduplicationEnabled: true
    },
    security: {
      encryptionEnabled: true,
      auditLogging: true,
      accessControlEnabled: true,
      rateLimitEnabled: true,
      maxRequestsPerMinute: 100
    },
    monitoring: {
      metricsEnabled: true,
      detailedLogging: false,
      performanceTracking: true,
      alertsEnabled: true,
      healthCheckInterval: 30
    }
  });

  const [showDangerZone, setShowDangerZone] = useState(false);

  const updateSetting = (category: keyof AdvancedSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    onSettingsChange(true);
  };

  const resetToDefaults = () => {
    if (confirm("Are you sure you want to reset all advanced settings to defaults? This action cannot be undone.")) {
      // Reset logic would go here
      onSettingsChange(true);
    }
  };

  const exportConfiguration = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', `evolution-advanced-config-${new Date().toISOString().split('T')[0]}.json`);
    linkElement.click();
  };

  const clearAllCaches = () => {
    if (confirm("This will clear all cached data. Evolution operations may be slower until caches are rebuilt. Continue?")) {
      // Clear cache logic would go here
    }
  };

  const rebuildIndexes = () => {
    if (confirm("This will rebuild all vector indexes. This may take several minutes and affect performance. Continue?")) {
      // Rebuild indexes logic would go here
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold">Advanced Configuration</h3>
        <p className="text-sm text-zinc-400">
          Fine-tune performance, security, and monitoring settings
        </p>
      </div>

      {/* Performance Settings */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Performance Optimization
          </CardTitle>
          <CardDescription>
            Configure processing performance and resource utilization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="batch-size">Batch Size</Label>
              <Input
                id="batch-size"
                type="number"
                value={settings.performance.batchSize}
                onChange={(e) => updateSetting('performance', 'batchSize', parseInt(e.target.value))}
                className="bg-zinc-800 border-zinc-700"
                min="1"
                max="1000"
              />
              <p className="text-xs text-zinc-400">Number of operations processed in each batch</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="max-concurrent">Max Concurrent Operations</Label>
              <Input
                id="max-concurrent"
                type="number"
                value={settings.performance.maxConcurrentOperations}
                onChange={(e) => updateSetting('performance', 'maxConcurrentOperations', parseInt(e.target.value))}
                className="bg-zinc-800 border-zinc-700"
                min="1"
                max="50"
              />
              <p className="text-xs text-zinc-400">Maximum parallel operations</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="timeout">Timeout (seconds)</Label>
              <Input
                id="timeout"
                type="number"
                value={settings.performance.timeoutSeconds}
                onChange={(e) => updateSetting('performance', 'timeoutSeconds', parseInt(e.target.value))}
                className="bg-zinc-800 border-zinc-700"
                min="5"
                max="300"
              />
              <p className="text-xs text-zinc-400">Operation timeout duration</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="retry-attempts">Retry Attempts</Label>
              <Input
                id="retry-attempts"
                type="number"
                value={settings.performance.retryAttempts}
                onChange={(e) => updateSetting('performance', 'retryAttempts', parseInt(e.target.value))}
                className="bg-zinc-800 border-zinc-700"
                min="0"
                max="10"
              />
              <p className="text-xs text-zinc-400">Number of retry attempts on failure</p>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="cache-enabled">Enable Caching</Label>
                <p className="text-xs text-zinc-400">Cache frequently accessed data for better performance</p>
              </div>
              <Switch
                id="cache-enabled"
                checked={settings.performance.cacheEnabled}
                onCheckedChange={(checked) => updateSetting('performance', 'cacheEnabled', checked)}
              />
            </div>
            
            {settings.performance.cacheEnabled && (
              <div className="space-y-2">
                <Label htmlFor="cache-ttl">Cache TTL (minutes)</Label>
                <Input
                  id="cache-ttl"
                  type="number"
                  value={settings.performance.cacheTtlMinutes}
                  onChange={(e) => updateSetting('performance', 'cacheTtlMinutes', parseInt(e.target.value))}
                  className="bg-zinc-800 border-zinc-700"
                  min="1"
                  max="1440"
                />
                <p className="text-xs text-zinc-400">Cache time-to-live in minutes</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Memory Management */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Memory Management
          </CardTitle>
          <CardDescription>
            Configure memory storage and vector processing settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="vector-dimensions">Vector Dimensions</Label>
              <Select
                value={settings.memory.vectorDimensions.toString()}
                onValueChange={(value) => updateSetting('memory', 'vectorDimensions', parseInt(value))}
              >
                <SelectTrigger className="bg-zinc-800 border-zinc-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="384">384 (Small)</SelectItem>
                  <SelectItem value="768">768 (Medium)</SelectItem>
                  <SelectItem value="1536">1536 (Large)</SelectItem>
                  <SelectItem value="3072">3072 (Extra Large)</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-zinc-400">Vector embedding dimensions</p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="max-memory-age">Max Memory Age (days)</Label>
              <Input
                id="max-memory-age"
                type="number"
                value={settings.memory.maxMemoryAge}
                onChange={(e) => updateSetting('memory', 'maxMemoryAge', parseInt(e.target.value))}
                className="bg-zinc-800 border-zinc-700"
                min="1"
                max="3650"
              />
              <p className="text-xs text-zinc-400">Maximum age before memory cleanup</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>Similarity Threshold: {settings.memory.similarityThreshold}</Label>
            <Slider
              value={[settings.memory.similarityThreshold]}
              onValueChange={(value) => updateSetting('memory', 'similarityThreshold', value[0])}
              max={1}
              min={0}
              step={0.01}
              className="w-full"
            />
            <p className="text-xs text-zinc-400">Minimum similarity score for memory matching</p>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Enable Compression</Label>
                <p className="text-xs text-zinc-400">Compress stored memories to save space</p>
              </div>
              <Switch
                checked={settings.memory.compressionEnabled}
                onCheckedChange={(checked) => updateSetting('memory', 'compressionEnabled', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Enable Deduplication</Label>
                <p className="text-xs text-zinc-400">Remove duplicate memories automatically</p>
              </div>
              <Switch
                checked={settings.memory.deduplicationEnabled}
                onCheckedChange={(checked) => updateSetting('memory', 'deduplicationEnabled', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security & Access Control
          </CardTitle>
          <CardDescription>
            Configure security features and access controls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Encryption</Label>
                  <p className="text-xs text-zinc-400">Encrypt sensitive data at rest</p>
                </div>
                <Switch
                  checked={settings.security.encryptionEnabled}
                  onCheckedChange={(checked) => updateSetting('security', 'encryptionEnabled', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Audit Logging</Label>
                  <p className="text-xs text-zinc-400">Log all system operations</p>
                </div>
                <Switch
                  checked={settings.security.auditLogging}
                  onCheckedChange={(checked) => updateSetting('security', 'auditLogging', checked)}
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Access Control</Label>
                  <p className="text-xs text-zinc-400">Enable role-based access control</p>
                </div>
                <Switch
                  checked={settings.security.accessControlEnabled}
                  onCheckedChange={(checked) => updateSetting('security', 'accessControlEnabled', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Rate Limiting</Label>
                  <p className="text-xs text-zinc-400">Limit API request rates</p>
                </div>
                <Switch
                  checked={settings.security.rateLimitEnabled}
                  onCheckedChange={(checked) => updateSetting('security', 'rateLimitEnabled', checked)}
                />
              </div>
            </div>
          </div>
          
          {settings.security.rateLimitEnabled && (
            <div className="space-y-2">
              <Label htmlFor="rate-limit">Max Requests per Minute</Label>
              <Input
                id="rate-limit"
                type="number"
                value={settings.security.maxRequestsPerMinute}
                onChange={(e) => updateSetting('security', 'maxRequestsPerMinute', parseInt(e.target.value))}
                className="bg-zinc-800 border-zinc-700"
                min="1"
                max="10000"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Monitoring Settings */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            Monitoring & Diagnostics
          </CardTitle>
          <CardDescription>
            Configure system monitoring and diagnostic features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Metrics Collection</Label>
                  <p className="text-xs text-zinc-400">Collect system performance metrics</p>
                </div>
                <Switch
                  checked={settings.monitoring.metricsEnabled}
                  onCheckedChange={(checked) => updateSetting('monitoring', 'metricsEnabled', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Detailed Logging</Label>
                  <p className="text-xs text-zinc-400">Enable verbose system logging</p>
                </div>
                <Switch
                  checked={settings.monitoring.detailedLogging}
                  onCheckedChange={(checked) => updateSetting('monitoring', 'detailedLogging', checked)}
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Performance Tracking</Label>
                  <p className="text-xs text-zinc-400">Track operation performance</p>
                </div>
                <Switch
                  checked={settings.monitoring.performanceTracking}
                  onCheckedChange={(checked) => updateSetting('monitoring', 'performanceTracking', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>System Alerts</Label>
                  <p className="text-xs text-zinc-400">Enable system health alerts</p>
                </div>
                <Switch
                  checked={settings.monitoring.alertsEnabled}
                  onCheckedChange={(checked) => updateSetting('monitoring', 'alertsEnabled', checked)}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="health-check-interval">Health Check Interval (seconds)</Label>
            <Input
              id="health-check-interval"
              type="number"
              value={settings.monitoring.healthCheckInterval}
              onChange={(e) => updateSetting('monitoring', 'healthCheckInterval', parseInt(e.target.value))}
              className="bg-zinc-800 border-zinc-700"
              min="10"
              max="3600"
            />
            <p className="text-xs text-zinc-400">Frequency of system health checks</p>
          </div>
        </CardContent>
      </Card>

      {/* System Operations */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Operations
          </CardTitle>
          <CardDescription>
            Maintenance and administrative operations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="outline"
              onClick={clearAllCaches}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <HardDrive className="mr-2 h-4 w-4" />
              Clear All Caches
            </Button>
            
            <Button
              variant="outline"
              onClick={rebuildIndexes}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Database className="mr-2 h-4 w-4" />
              Rebuild Indexes
            </Button>
            
            <Button
              variant="outline"
              onClick={exportConfiguration}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Network className="mr-2 h-4 w-4" />
              Export Configuration
            </Button>
            
            <Button
              variant="outline"
              onClick={resetToDefaults}
              className="border-zinc-700 hover:bg-zinc-800"
            >
              <Settings className="mr-2 h-4 w-4" />
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="bg-red-900/10 border-red-800/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-400">
            <AlertTriangle className="h-5 w-5" />
            Danger Zone
          </CardTitle>
          <CardDescription className="text-red-300/70">
            Irreversible operations that can affect system stability
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="outline"
            onClick={() => setShowDangerZone(!showDangerZone)}
            className="border-red-800 text-red-400 hover:bg-red-900/20"
          >
            {showDangerZone ? "Hide" : "Show"} Danger Zone
          </Button>
          
          {showDangerZone && (
            <div className="mt-4 space-y-3">
              <div className="p-4 bg-red-900/20 border border-red-800/50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-red-400" />
                  <span className="font-medium text-red-400">Reset All Evolution Data</span>
                </div>
                <p className="text-sm text-red-300/70 mb-3">
                  This will permanently delete all evolution analytics, configurations, and cached data.
                </p>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    if (confirm("This will permanently delete ALL evolution data. Type 'DELETE' to confirm.")) {
                      const confirmation = prompt("Type 'DELETE' to confirm:");
                      if (confirmation === "DELETE") {
                        // Reset all data logic would go here
                      }
                    }
                  }}
                >
                  Reset All Data
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
