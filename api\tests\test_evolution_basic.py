import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker


class TestEvolutionBasic:
    """Basic evolution intelligence tests using raw SQL to avoid ORM schema issues."""
    
    @pytest.fixture(autouse=True)
    def setup_test_db(self):
        """Set up a clean test database for each test."""
        # Use in-memory SQLite database
        self.test_engine = create_engine('sqlite:///:memory:', echo=False)
        
        try:
            # Create tables with raw SQL
            with self.test_engine.connect() as conn:
                # Create users table
                conn.execute(text("""
                    CREATE TABLE users (
                        id TEXT PRIMARY KEY,
                        user_id TEXT NOT NULL UNIQUE,
                        name TEXT,
                        email TEXT UNIQUE,
                        metadata TEXT DEFAULT '{}',
                        created_at DATETIME,
                        updated_at DATETIME,
                        supabase_user_id TEXT UNIQUE,
                        email_verified BOOLEAN DEFAULT 0,
                        last_sign_in_at DATETIME
                    )
                """))
                
                # Create apps table
                conn.execute(text("""
                    CREATE TABLE apps (
                        id TEXT PRIMARY KEY,
                        owner_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        metadata TEXT DEFAULT '{}',
                        is_active BOOLEAN DEFAULT 1,
                        created_at DATETIME,
                        updated_at DATETIME,
                        FOREIGN KEY (owner_id) REFERENCES users (id),
                        UNIQUE (owner_id, name)
                    )
                """))
                
                # Create memories table
                conn.execute(text("""
                    CREATE TABLE memories (
                        id TEXT PRIMARY KEY,
                        content TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        app_id TEXT,
                        metadata TEXT DEFAULT '{}',
                        created_at DATETIME,
                        updated_at DATETIME,
                        state TEXT DEFAULT 'active',
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        FOREIGN KEY (app_id) REFERENCES apps (id)
                    )
                """))
                
                # Create evolution_operations table
                conn.execute(text("""
                    CREATE TABLE evolution_operations (
                        id TEXT PRIMARY KEY,
                        operation_type TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        app_id TEXT,
                        memory_id TEXT,
                        input_data TEXT,
                        output_data TEXT,
                        metadata TEXT DEFAULT '{}',
                        created_at DATETIME,
                        processing_time_ms INTEGER,
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        FOREIGN KEY (app_id) REFERENCES apps (id),
                        FOREIGN KEY (memory_id) REFERENCES memories (id)
                    )
                """))
                
                # Create evolution_insights table
                conn.execute(text("""
                    CREATE TABLE evolution_insights (
                        id TEXT PRIMARY KEY,
                        user_id TEXT NOT NULL,
                        app_id TEXT,
                        insight_type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        confidence_score REAL,
                        metadata TEXT DEFAULT '{}',
                        created_at DATETIME,
                        is_applied BOOLEAN DEFAULT 0,
                        applied_at DATETIME,
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        FOREIGN KEY (app_id) REFERENCES apps (id)
                    )
                """))
                
                conn.commit()
            
            # Create session
            TestSession = sessionmaker(bind=self.test_engine)
            self.session = TestSession()
            
            # Create test data
            self.test_user_id = str(uuid.uuid4())
            self.test_app_id = str(uuid.uuid4())
            self.test_memory_id = str(uuid.uuid4())
            
            now = datetime.utcnow()
            
            with self.test_engine.connect() as conn:
                # Insert test user
                conn.execute(text("""
                    INSERT INTO users (id, user_id, name, email, metadata, created_at, updated_at, email_verified)
                    VALUES (:id, :user_id, :name, :email, :metadata, :created_at, :updated_at, :email_verified)
                """), {
                    'id': self.test_user_id,
                    'user_id': 'test_user_123',
                    'name': 'Test User',
                    'email': '<EMAIL>',
                    'metadata': '{}',
                    'created_at': now,
                    'updated_at': now,
                    'email_verified': False
                })
                
                # Insert test app
                conn.execute(text("""
                    INSERT INTO apps (id, owner_id, name, description, metadata, is_active, created_at, updated_at)
                    VALUES (:id, :owner_id, :name, :description, :metadata, :is_active, :created_at, :updated_at)
                """), {
                    'id': self.test_app_id,
                    'owner_id': self.test_user_id,
                    'name': 'Test App',
                    'description': 'Test application',
                    'metadata': '{}',
                    'is_active': True,
                    'created_at': now,
                    'updated_at': now
                })
                
                # Insert test memory
                conn.execute(text("""
                    INSERT INTO memories (id, content, user_id, app_id, metadata, created_at, updated_at, state)
                    VALUES (:id, :content, :user_id, :app_id, :metadata, :created_at, :updated_at, :state)
                """), {
                    'id': self.test_memory_id,
                    'content': 'Test memory content',
                    'user_id': self.test_user_id,
                    'app_id': self.test_app_id,
                    'metadata': '{}',
                    'created_at': now,
                    'updated_at': now,
                    'state': 'active'
                })
                
                conn.commit()
            
            yield
            
        finally:
            # Cleanup
            if hasattr(self, 'session'):
                self.session.close()
            if hasattr(self, 'test_engine'):
                self.test_engine.dispose()
    
    def test_evolution_operation_creation(self):
        """Test creating an evolution operation record."""
        operation_id = str(uuid.uuid4())
        now = datetime.utcnow()
        
        with self.test_engine.connect() as conn:
            # Insert evolution operation
            conn.execute(text("""
                INSERT INTO evolution_operations (
                    id, operation_type, user_id, app_id, memory_id, 
                    input_data, output_data, metadata, created_at, processing_time_ms
                )
                VALUES (
                    :id, :operation_type, :user_id, :app_id, :memory_id,
                    :input_data, :output_data, :metadata, :created_at, :processing_time_ms
                )
            """), {
                'id': operation_id,
                'operation_type': 'ANALYZE',
                'user_id': self.test_user_id,
                'app_id': self.test_app_id,
                'memory_id': self.test_memory_id,
                'input_data': '{"query": "test"}',
                'output_data': '{"result": "analysis complete"}',
                'metadata': '{}',
                'created_at': now,
                'processing_time_ms': 150
            })
            
            conn.commit()
            
            # Verify the operation was created
            result = conn.execute(text("""
                SELECT id, operation_type, user_id, processing_time_ms
                FROM evolution_operations
                WHERE id = :id
            """), {'id': operation_id})
            
            row = result.fetchone()
            assert row is not None
            assert row[0] == operation_id
            assert row[1] == 'ANALYZE'
            assert row[2] == self.test_user_id
            assert row[3] == 150
    
    def test_evolution_insight_creation(self):
        """Test creating an evolution insight record."""
        insight_id = str(uuid.uuid4())
        now = datetime.utcnow()
        
        with self.test_engine.connect() as conn:
            # Insert evolution insight
            conn.execute(text("""
                INSERT INTO evolution_insights (
                    id, user_id, app_id, insight_type, content,
                    confidence_score, metadata, created_at, is_applied
                )
                VALUES (
                    :id, :user_id, :app_id, :insight_type, :content,
                    :confidence_score, :metadata, :created_at, :is_applied
                )
            """), {
                'id': insight_id,
                'user_id': self.test_user_id,
                'app_id': self.test_app_id,
                'insight_type': 'PATTERN',
                'content': 'User frequently asks about technical topics',
                'confidence_score': 0.85,
                'metadata': '{}',
                'created_at': now,
                'is_applied': False
            })
            
            conn.commit()
            
            # Verify the insight was created
            result = conn.execute(text("""
                SELECT id, insight_type, content, confidence_score
                FROM evolution_insights
                WHERE id = :id
            """), {'id': insight_id})
            
            row = result.fetchone()
            assert row is not None
            assert row[0] == insight_id
            assert row[1] == 'PATTERN'
            assert row[2] == 'User frequently asks about technical topics'
            assert row[3] == 0.85
    
    def test_database_schema_validation(self):
        """Test that all required tables and columns exist."""
        with self.test_engine.connect() as conn:
            # Check users table
            result = conn.execute(text("PRAGMA table_info(users)"))
            columns = [row[1] for row in result.fetchall()]
            expected_user_columns = ['id', 'user_id', 'name', 'email', 'metadata', 
                                   'created_at', 'updated_at', 'supabase_user_id', 
                                   'email_verified', 'last_sign_in_at']
            for col in expected_user_columns:
                assert col in columns, f"Missing column {col} in users table"
            
            # Check evolution_operations table
            result = conn.execute(text("PRAGMA table_info(evolution_operations)"))
            columns = [row[1] for row in result.fetchall()]
            expected_evo_columns = ['id', 'operation_type', 'user_id', 'app_id', 
                                  'memory_id', 'input_data', 'output_data', 
                                  'metadata', 'created_at', 'processing_time_ms']
            for col in expected_evo_columns:
                assert col in columns, f"Missing column {col} in evolution_operations table"
            
            # Check evolution_insights table
            result = conn.execute(text("PRAGMA table_info(evolution_insights)"))
            columns = [row[1] for row in result.fetchall()]
            expected_insight_columns = ['id', 'user_id', 'app_id', 'insight_type', 
                                      'content', 'confidence_score', 'metadata', 
                                      'created_at', 'is_applied', 'applied_at']
            for col in expected_insight_columns:
                assert col in columns, f"Missing column {col} in evolution_insights table"