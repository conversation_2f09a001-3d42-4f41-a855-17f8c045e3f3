"""
Rate limiting and brute force protection utilities.
"""
import time
import logging
from typing import Dict, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading

logger = logging.getLogger(__name__)

class RateLimiter:
    """
    In-memory rate limiter for authentication endpoints.
    In production, consider using Redis for distributed rate limiting.
    """
    
    def __init__(self):
        self._attempts = defaultdict(deque)
        self._lock = threading.Lock()
        
    def is_rate_limited(self, identifier: str, max_attempts: int = 5, window_minutes: int = 15) -> bool:
        """
        Check if an identifier (IP address, user ID, etc.) is rate limited.
        
        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            max_attempts: Maximum attempts allowed in the time window
            window_minutes: Time window in minutes
            
        Returns:
            True if rate limited, False otherwise
        """
        with self._lock:
            now = time.time()
            window_start = now - (window_minutes * 60)
            
            # Get attempts for this identifier
            attempts = self._attempts[identifier]
            
            # Remove old attempts outside the time window
            while attempts and attempts[0] < window_start:
                attempts.popleft()
            
            # Check if we've exceeded the limit
            if len(attempts) >= max_attempts:
                logger.warning(f"Rate limit exceeded for {identifier}: {len(attempts)} attempts in {window_minutes} minutes")
                return True
            
            return False
    
    def record_attempt(self, identifier: str):
        """Record a new attempt for the identifier."""
        with self._lock:
            self._attempts[identifier].append(time.time())
    
    def get_attempt_count(self, identifier: str, window_minutes: int = 15) -> int:
        """Get the number of attempts for an identifier in the time window."""
        with self._lock:
            now = time.time()
            window_start = now - (window_minutes * 60)
            
            attempts = self._attempts[identifier]
            
            # Remove old attempts
            while attempts and attempts[0] < window_start:
                attempts.popleft()
            
            return len(attempts)
    
    def reset_attempts(self, identifier: str):
        """Reset attempts for an identifier (useful after successful login)."""
        with self._lock:
            if identifier in self._attempts:
                del self._attempts[identifier]
    
    def cleanup_old_entries(self, max_age_hours: int = 24):
        """Clean up old entries to prevent memory leaks."""
        with self._lock:
            cutoff_time = time.time() - (max_age_hours * 3600)
            
            identifiers_to_remove = []
            for identifier, attempts in self._attempts.items():
                # Remove old attempts
                while attempts and attempts[0] < cutoff_time:
                    attempts.popleft()
                
                # If no recent attempts, mark for removal
                if not attempts:
                    identifiers_to_remove.append(identifier)
            
            # Remove empty entries
            for identifier in identifiers_to_remove:
                del self._attempts[identifier]
            
            if identifiers_to_remove:
                logger.info(f"Cleaned up {len(identifiers_to_remove)} old rate limiting entries")

class BruteForceProtection:
    """
    Enhanced brute force protection with progressive delays and lockouts.
    """
    
    def __init__(self):
        self._failed_attempts = defaultdict(list)
        self._lockouts = {}
        self._lock = threading.Lock()
    
    def check_and_record_failure(self, identifier: str, max_failures: int = 3, 
                                lockout_minutes: int = 30) -> Dict[str, any]:
        """
        Check if identifier is locked out and record a failed attempt.
        
        Returns:
            Dict with 'blocked', 'remaining_attempts', 'lockout_until' keys
        """
        with self._lock:
            now = datetime.utcnow()
            
            # Check if currently locked out
            if identifier in self._lockouts:
                lockout_until = self._lockouts[identifier]
                if now < lockout_until:
                    remaining_minutes = int((lockout_until - now).total_seconds() / 60)
                    logger.warning(f"Blocked login attempt for {identifier} - locked out for {remaining_minutes} more minutes")
                    return {
                        'blocked': True,
                        'remaining_attempts': 0,
                        'lockout_until': lockout_until.isoformat(),
                        'remaining_minutes': remaining_minutes
                    }
                else:
                    # Lockout expired, remove it
                    del self._lockouts[identifier]
            
            # Record the failed attempt
            self._failed_attempts[identifier].append(now)
            
            # Clean up attempts older than 1 hour
            cutoff = now - timedelta(hours=1)
            self._failed_attempts[identifier] = [
                attempt for attempt in self._failed_attempts[identifier] 
                if attempt > cutoff
            ]
            
            recent_failures = len(self._failed_attempts[identifier])
            remaining_attempts = max(0, max_failures - recent_failures)
            
            # Check if we should lock out
            if recent_failures >= max_failures:
                lockout_until = now + timedelta(minutes=lockout_minutes)
                self._lockouts[identifier] = lockout_until
                
                logger.error(f"Locking out {identifier} after {recent_failures} failed attempts until {lockout_until}")
                
                return {
                    'blocked': True,
                    'remaining_attempts': 0,
                    'lockout_until': lockout_until.isoformat(),
                    'remaining_minutes': lockout_minutes
                }
            
            return {
                'blocked': False,
                'remaining_attempts': remaining_attempts,
                'lockout_until': None,
                'remaining_minutes': 0
            }
    
    def record_success(self, identifier: str):
        """Record a successful login and clear failed attempts."""
        with self._lock:
            if identifier in self._failed_attempts:
                del self._failed_attempts[identifier]
            if identifier in self._lockouts:
                del self._lockouts[identifier]
    
    def is_locked_out(self, identifier: str) -> bool:
        """Check if an identifier is currently locked out."""
        with self._lock:
            if identifier not in self._lockouts:
                return False
            
            lockout_until = self._lockouts[identifier]
            if datetime.utcnow() >= lockout_until:
                # Lockout expired
                del self._lockouts[identifier]
                return False
            
            return True

# Global instances
rate_limiter = RateLimiter()
brute_force_protection = BruteForceProtection()

def get_client_ip(request) -> str:
    """
    Extract client IP address from request, handling proxies.
    """
    # Check for forwarded IP first (behind proxy)
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        # Take the first IP in the chain
        return forwarded_for.split(',')[0].strip()
    
    # Check for real IP header
    real_ip = request.headers.get('X-Real-IP')
    if real_ip:
        return real_ip
    
    # Fall back to direct connection IP
    return getattr(request.client, 'host', 'unknown')

# Background cleanup task
def cleanup_rate_limiter():
    """Background task to clean up old rate limiter entries."""
    rate_limiter.cleanup_old_entries()

# Schedule cleanup to run periodically (would need a background task scheduler in production)
# For now, this needs to be called manually or integrated with your task scheduler