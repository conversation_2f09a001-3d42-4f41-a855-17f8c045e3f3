import { useState, useCallback } from 'react';
import { apiGet, buildApiUrl, ApiError } from '@/lib/api-client';

export interface EvolutionAnalyticsItem {
  id: string;
  user_id: string;
  app_id?: string;
  config_id?: string;
  operation_type: 'ADD' | 'UPDATE' | 'DELETE' | 'NOOP';
  confidence_score: number;
  similarity_score?: number;
  reasoning?: string;
  processing_time_ms?: number;
  metadata_?: Record<string, any>;
  timestamp: string;
  candidate_fact?: string;  // Add the actual memory content
  existing_memory_content?: string;
}

export interface PaginatedEvolutionAnalyticsResponse {
  items: EvolutionAnalyticsItem[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface TimelineDataPoint {
  timestamp: string;
  date: Date;
  ADD: number;
  UPDATE: number;
  DELETE: number;
  NOOP: number;
  total: number;
}

export interface UseEvolutionAnalyticsReturn {
  isLoading: boolean;
  error: string | null;
  fetchAnalytics: (params?: {
    page?: number;
    size?: number;
    app_id?: string;
    config_id?: string;
    operation_type?: string;
  }) => Promise<PaginatedEvolutionAnalyticsResponse>;
  fetchTimelineData: (timeframe: '24h' | '30d' | '12w' | '12m') => Promise<TimelineDataPoint[]>;
  exportToCsv: (data: EvolutionAnalyticsItem[], filename?: string) => void;
}

export const useEvolutionAnalytics = (): UseEvolutionAnalyticsReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = useCallback(async (params: {
    page?: number;
    size?: number;
    app_id?: string;
    config_id?: string;
    operation_type?: string;
  } = {}): Promise<PaginatedEvolutionAnalyticsResponse> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.size) queryParams.append('size', params.size.toString());
      if (params.app_id) queryParams.append('app_id', params.app_id);
      if (params.config_id) queryParams.append('config_id', params.config_id);
      if (params.operation_type) queryParams.append('operation_type', params.operation_type);

      const response = await apiGet<PaginatedEvolutionAnalyticsResponse>(
        buildApiUrl(`/api/v1/evolution-config/analytics?${queryParams.toString()}`)
      );
      
      setIsLoading(false);
      return response;
    } catch (err: any) {
      const errorMessage = err instanceof ApiError ? err.message : (err.message || 'Failed to fetch evolution analytics');
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  }, []);

  const fetchTimelineData = useCallback(async (timeframe: '24h' | '30d' | '12w' | '12m'): Promise<TimelineDataPoint[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Calculate date range based on timeframe
      const now = new Date();
      let startDate: Date;
      let groupBy: 'hour' | 'day' | 'week' | 'month';
      
      switch (timeframe) {
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          groupBy = 'hour';
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          groupBy = 'day';
          break;
        case '12w':
          startDate = new Date(now.getTime() - 12 * 7 * 24 * 60 * 60 * 1000);
          groupBy = 'week';
          break;
        case '12m':
          startDate = new Date(now.getTime() - 12 * 30 * 24 * 60 * 60 * 1000);
          groupBy = 'month';
          break;
      }

      // Fetch all analytics data for the time period
      const response = await fetchAnalytics({ size: 1000 }); // Get large batch
      
      // Filter data by date range
      const filteredData = response.items.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= startDate && itemDate <= now;
      });

      // Group data by time periods
      const groupedData = new Map<string, { ADD: number; UPDATE: number; DELETE: number; NOOP: number }>();
      
      filteredData.forEach(item => {
        const itemDate = new Date(item.timestamp);
        let key: string;
        
        switch (groupBy) {
          case 'hour':
            key = `${itemDate.getFullYear()}-${String(itemDate.getMonth() + 1).padStart(2, '0')}-${String(itemDate.getDate()).padStart(2, '0')} ${String(itemDate.getHours()).padStart(2, '0')}:00`;
            break;
          case 'day':
            key = `${itemDate.getFullYear()}-${String(itemDate.getMonth() + 1).padStart(2, '0')}-${String(itemDate.getDate()).padStart(2, '0')}`;
            break;
          case 'week':
            const weekStart = new Date(itemDate);
            weekStart.setDate(itemDate.getDate() - itemDate.getDay());
            key = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getTime() - new Date(weekStart.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`;
            break;
          case 'month':
            key = `${itemDate.getFullYear()}-${String(itemDate.getMonth() + 1).padStart(2, '0')}`;
            break;
        }
        
        if (!groupedData.has(key)) {
          groupedData.set(key, { ADD: 0, UPDATE: 0, DELETE: 0, NOOP: 0 });
        }
        
        const group = groupedData.get(key)!;
        group[item.operation_type]++;
      });

      // Convert to timeline data points
      const timelineData: TimelineDataPoint[] = Array.from(groupedData.entries()).map(([timestamp, counts]) => ({
        timestamp,
        date: new Date(timestamp),
        ADD: counts.ADD,
        UPDATE: counts.UPDATE,
        DELETE: counts.DELETE,
        NOOP: counts.NOOP,
        total: counts.ADD + counts.UPDATE + counts.DELETE + counts.NOOP
      })).sort((a, b) => a.date.getTime() - b.date.getTime());

      setIsLoading(false);
      return timelineData;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch timeline data';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  }, [fetchAnalytics]);

  const exportToCsv = useCallback((data: EvolutionAnalyticsItem[], filename: string = 'evolution-analytics.csv') => {
    // Dynamic import to avoid SSR issues
    import('papaparse').then((Papa) => {
      const csv = Papa.unparse(data.map(item => ({
        id: item.id,
        timestamp: item.timestamp,
        operation_type: item.operation_type,
        confidence_score: item.confidence_score,
        similarity_score: item.similarity_score || '',
        reasoning: item.reasoning || '',
        processing_time_ms: item.processing_time_ms || '',
        app_id: item.app_id || '',
        config_id: item.config_id || ''
      })));
      
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }, []);

  return {
    isLoading,
    error,
    fetchAnalytics,
    fetchTimelineData,
    exportToCsv
  };
};
