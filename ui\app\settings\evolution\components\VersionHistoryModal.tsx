"use client";

import { useState } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  History, 
  Clock, 
  User, 
  FileText, 
  Download,
  RotateCcw,
  Eye,
  GitBranch,
  Diff
} from "lucide-react";

interface PromptVersion {
  id: string;
  version: string;
  content: string;
  timestamp: string;
  author: string;
  changes: string;
}

interface VersionHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  versions: PromptVersion[];
  onRestoreVersion: (version: PromptVersion) => void;
}

export function VersionHistoryModal({ 
  isOpen, 
  onClose, 
  versions, 
  onRestoreVersion 
}: VersionHistoryModalProps) {
  const [selectedVersion, setSelectedVersion] = useState<PromptVersion | null>(null);
  const [compareVersion, setCompareVersion] = useState<PromptVersion | null>(null);
  const [showDiff, setShowDiff] = useState(false);

  const sortedVersions = [...versions].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  const generateDiff = (oldContent: string, newContent: string) => {
    // Simple diff implementation - in a real app, you'd use a proper diff library
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    const maxLines = Math.max(oldLines.length, newLines.length);
    
    const diff = [];
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (oldLine !== newLine) {
        if (oldLine && !newLine) {
          diff.push({ type: 'removed', content: oldLine, lineNumber: i + 1 });
        } else if (!oldLine && newLine) {
          diff.push({ type: 'added', content: newLine, lineNumber: i + 1 });
        } else {
          diff.push({ type: 'removed', content: oldLine, lineNumber: i + 1 });
          diff.push({ type: 'added', content: newLine, lineNumber: i + 1 });
        }
      } else if (oldLine) {
        diff.push({ type: 'unchanged', content: oldLine, lineNumber: i + 1 });
      }
    }
    
    return diff;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[80vh] bg-zinc-900 border-zinc-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Version History
          </DialogTitle>
          <DialogDescription>
            View and restore previous versions of your prompts
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[500px]">
          {/* Version List */}
          <div className="lg:col-span-1">
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <GitBranch className="h-4 w-4" />
              Versions ({sortedVersions.length})
            </h4>
            
            <ScrollArea className="h-[450px] pr-2">
              <div className="space-y-2">
                {sortedVersions.map((version, index) => (
                  <Card 
                    key={version.id} 
                    className={`cursor-pointer transition-colors ${
                      selectedVersion?.id === version.id 
                        ? 'bg-primary/20 border-primary/50' 
                        : 'bg-zinc-800/50 border-zinc-700 hover:border-zinc-600'
                    }`}
                    onClick={() => setSelectedVersion(version)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant={index === 0 ? "default" : "outline"} className="text-xs">
                          v{version.version}
                        </Badge>
                        {index === 0 && (
                          <Badge variant="secondary" className="text-xs">
                            Current
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm font-medium mb-1">{version.changes}</p>
                      
                      <div className="flex items-center gap-2 text-xs text-zinc-400">
                        <User className="h-3 w-3" />
                        <span>{version.author}</span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-xs text-zinc-400 mt-1">
                        <Clock className="h-3 w-3" />
                        <span>{getRelativeTime(version.timestamp)}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Version Details */}
          <div className="lg:col-span-2">
            {selectedVersion ? (
              <Tabs defaultValue="content" className="h-full">
                <div className="flex items-center justify-between mb-3">
                  <TabsList className="bg-zinc-800 border border-zinc-700">
                    <TabsTrigger value="content">Content</TabsTrigger>
                    <TabsTrigger value="diff" disabled={!compareVersion}>
                      Diff View
                    </TabsTrigger>
                  </TabsList>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCompareVersion(selectedVersion)}
                      className="border-zinc-700"
                    >
                      <Diff className="mr-1 h-3 w-3" />
                      Compare
                    </Button>
                    
                    {selectedVersion.version !== sortedVersions[0]?.version && (
                      <Button
                        size="sm"
                        onClick={() => onRestoreVersion(selectedVersion)}
                        className="bg-primary hover:bg-primary/90"
                      >
                        <RotateCcw className="mr-1 h-3 w-3" />
                        Restore
                      </Button>
                    )}
                  </div>
                </div>

                <TabsContent value="content" className="h-[400px]">
                  <Card className="h-full bg-zinc-800/50 border-zinc-700">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm">
                          Version {selectedVersion.version}
                        </CardTitle>
                        <div className="text-xs text-zinc-400">
                          {formatDate(selectedVersion.timestamp)}
                        </div>
                      </div>
                      <CardDescription className="text-xs">
                        {selectedVersion.changes}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent>
                      <ScrollArea className="h-[280px]">
                        <pre className="text-sm whitespace-pre-wrap font-mono">
                          {selectedVersion.content}
                        </pre>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="diff" className="h-[400px]">
                  {compareVersion && selectedVersion && (
                    <Card className="h-full bg-zinc-800/50 border-zinc-700">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">
                          Comparing v{compareVersion.version} → v{selectedVersion.version}
                        </CardTitle>
                      </CardHeader>
                      
                      <CardContent>
                        <ScrollArea className="h-[300px]">
                          <div className="space-y-1 font-mono text-sm">
                            {generateDiff(compareVersion.content, selectedVersion.content).map((line, index) => (
                              <div
                                key={index}
                                className={`px-2 py-1 ${
                                  line.type === 'added' 
                                    ? 'bg-green-900/30 text-green-300' 
                                    : line.type === 'removed'
                                    ? 'bg-red-900/30 text-red-300'
                                    : 'text-zinc-300'
                                }`}
                              >
                                <span className="text-zinc-500 mr-4 text-xs">
                                  {line.lineNumber.toString().padStart(3, ' ')}
                                </span>
                                <span className="mr-2">
                                  {line.type === 'added' ? '+' : line.type === 'removed' ? '-' : ' '}
                                </span>
                                {line.content}
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
              </Tabs>
            ) : (
              <div className="h-full flex items-center justify-center text-zinc-400">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a version to view details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
