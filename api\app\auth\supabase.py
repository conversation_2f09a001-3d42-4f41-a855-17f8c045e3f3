import os
import time
from typing import Optional
from supabase import create_client, Client
from jose import JW<PERSON><PERSON>r, jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status
import logging

logger = logging.getLogger(__name__)

class SupabaseClient:
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") 
        self.jwt_secret = os.getenv("SUPABASE_JWT_SECRET")
        self.auth_enabled = os.getenv("AUTH_ENABLED", "false").lower() == "true"
        
        # JWT expiration settings (in seconds)
        self.jwt_access_token_expiry = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRY", "3600"))  # 1 hour
        self.jwt_refresh_token_expiry = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRY", "604800"))  # 7 days
        
        if self.auth_enabled and not all([self.url, self.service_role_key, self.jwt_secret]):
            logger.warning("Supabase configuration incomplete. Authentication will be disabled.")
            self.auth_enabled = False
        
        self.client: Optional[Client] = None
        if self.auth_enabled:
            try:
                # Create client with enhanced configuration
                self.client = create_client(self.url, self.service_role_key)
                logger.info(f"Supabase client initialized successfully for {self.url}")
                logger.info(f"JWT settings - Access token expiry: {self.jwt_access_token_expiry}s, Refresh token expiry: {self.jwt_refresh_token_expiry}s")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                self.auth_enabled = False

    def verify_jwt_token(self, token: str) -> Optional[dict]:
        """
        Verify and decode a Supabase JWT token.
        Returns the decoded payload if valid, None otherwise.
        """
        if not self.auth_enabled or not self.jwt_secret:
            return None
            
        try:
            # Decode and verify the JWT token
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=["HS256"],
                audience="authenticated"
            )
            
            # Validate token expiry
            if not self.validate_token_expiry(payload):
                return None
            
            return payload
        except JWTError as e:
            logger.warning(f"JWT verification failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during JWT verification: {e}")
            return None

    def get_user_by_id(self, user_id: str) -> Optional[dict]:
        """
        Get user information from Supabase by user ID.
        """
        if not self.auth_enabled or not self.client:
            return None
            
        try:
            response = self.client.auth.admin.get_user_by_id(user_id)
            return response.user.dict() if response.user else None
        except Exception as e:
            logger.error(f"Failed to get user by ID: {e}")
            return None

    def create_user_profile(self, user_data: dict) -> bool:
        """
        Create or update user profile in our internal database.
        """
        # This will be implemented when we integrate with the User model
        pass

    def retry_operation(self, operation_func, max_retries: int = 3, delay: float = 1.0):
        """
        Retry an operation with exponential backoff for network failures.
        """
        for attempt in range(max_retries):
            try:
                return operation_func()
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Operation failed after {max_retries} attempts: {e}")
                    raise e
                
                wait_time = delay * (2 ** attempt)
                logger.warning(f"Operation failed (attempt {attempt + 1}/{max_retries}), retrying in {wait_time}s: {e}")
                time.sleep(wait_time)

    def validate_token_expiry(self, payload: dict) -> bool:
        """
        Validate that the JWT token is not expired.
        """
        if 'exp' not in payload:
            logger.warning("JWT token missing expiration claim")
            return False
        
        current_time = int(time.time())
        exp_time = payload['exp']
        
        if current_time >= exp_time:
            logger.warning(f"JWT token expired: current={current_time}, exp={exp_time}")
            return False
        
        # Log warning if token expires within 5 minutes
        if (exp_time - current_time) < 300:
            logger.info(f"JWT token expires soon: {exp_time - current_time} seconds remaining")
        
        return True

    def get_auth_status(self) -> dict:
        """
        Get comprehensive authentication status information.
        """
        return {
            "auth_enabled": self.auth_enabled,
            "supabase_url": self.url if self.auth_enabled else None,
            "client_initialized": self.client is not None,
            "jwt_access_token_expiry": self.jwt_access_token_expiry,
            "jwt_refresh_token_expiry": self.jwt_refresh_token_expiry,
            "configuration_complete": all([self.url, self.service_role_key, self.jwt_secret]) if self.auth_enabled else False
        }

# Global instance
supabase_client = SupabaseClient()